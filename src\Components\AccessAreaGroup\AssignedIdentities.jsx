import React, { useState } from "react";
import GenericTable from "../GenericTable";
import AssignedIdentitiesAdd from "./AssignedIdentitiesAdd";
import { MdOutlineDeleteForever } from "react-icons/md";
import ViewEditAreaFacilityForm from "../Facility/AccessArea/ViewEditAccessAreaForm"; // if you have a similar view/edit form
import TruncatedCell from "../Tooltip/TruncatedCell";
import TruncatedRow from "../Tooltip/TrucantedRow";
import { AssignedIdentitiesData } from "../../api/static";

const AssignedIdentities = () => {
  // Sample initial data with the required fields
  const [data, setData] = useState(AssignedIdentitiesData);
  const [searchTerm, setSearchTerm] = useState("");
  const [showAddForm, setShowAddForm] = useState(false);
  const [showViewForm, setShowViewForm] = useState(false);
  const [selectedIdentity, setSelectedIdentity] = useState(null);

  const columns = [
    {
      name: "Name",
      selector: (row) => row.name,
      cell : row => <TruncatedRow text={row.name}/>,
      sortable: true,
     
    },
    {
      name: "EID",
      selector: (row) => row.eid,
      cell : row => <TruncatedRow text={row.eid}/>,
      sortable: true,
    },
    {
      name: "Type",
      selector: (row) => row.type,
      cell : row => <TruncatedRow text={row.type}/>,
      sortable: true,
    },
    {
      name: <TruncatedCell text="Company"/>,
      selector: (row) => row.company,
      cell : row => <TruncatedRow text={row.company}/>,
      sortable: true,
    },
    {
      name: <TruncatedCell text="Organization"/>,
      selector: (row) => row.organization,
      cell : row => <TruncatedRow text={row.organization}/>,
      sortable: true,
    },
    {
      name: <TruncatedCell text="Job Title"/>,
      selector: (row) => row.jobTitle,
      cell : row => <TruncatedRow text={row.jobTitle}/>,
      sortable: true,
    },
    {
      name: <TruncatedCell text="End Date"/>,
      selector: (row) => row.endDate,
      cell : row => <TruncatedRow text={row.endDate}/>,
      sortable: true,
    },
    {
      name: "Status",
      selector: (row) => row.status,
      sortable: true,
      cell: (row) => (
        <span
          className={`w-20 py-1 flex justify-center items-center text-sm font-semibold rounded-full ${
            row.status.toLowerCase() === "active"
              ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
              : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
          }`}
        >
          {row.status}
        </span>
      ),
    },
    {
      name: "Action",
      cell: (row) => (
        <MdOutlineDeleteForever
          size={32}
         className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5] text-red-600"
          onClick={() => handleDelete(row.id)}
        />
      ),
    },
  ];

  const handleAdd = () => {
    setShowAddForm(true);
  };

  const handleCloseAddModal = () => {
    setShowAddForm(false);
  };

  const handleView = (row) => {
    setSelectedIdentity(row);
    setShowViewForm(true);
  };

  const handleUpdate = (updatedIdentity) => {
    setData((prevData) =>
      prevData.map((item) =>
        item.id === updatedIdentity.id ? { ...item, ...updatedIdentity } : item
      )
    );
    setShowViewForm(false);
  };

  const handleCloseViewModal = () => {
    setShowViewForm(false);
  };

  const handleDelete = (id) => {
    setData((prevData) => prevData.filter((item) => item.id !== id));
  };

  return (
    <div className="bg-white rounded-[10px]">
      <GenericTable
        title="Assigned Identities"
        searchTerm={searchTerm}
        showSearch={true}
        onSearchChange={(e) => setSearchTerm(e.target.value)}
        onAdd={handleAdd}
        columns={columns}
        data={data}
        showAddButton={true}
      />
      {showAddForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-10 flex justify-center items-center">
        <div className="bg-white shadow-lg p-1 rounded-lg w-[80%]">
        <div className="rounded-lg max-h-[90vh] ">
              <AssignedIdentitiesAdd
                onClose={handleCloseAddModal}
                onSubmit={(newIdentity, action) => {
                  const identityToAdd = { ...newIdentity, id: Date.now() };
                  setData([identityToAdd, ...data]);
                  if (action === "add") {
                    setShowAddForm(false);
                  }
                }}
                availableIdentities={data}
              />
            </div>
          </div>
        </div>
      )}
      {showViewForm && selectedIdentity && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-10 flex justify-center items-center">
          <div className="bg-white shadow-lg p-1 rounded-lg w-[80%]">
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
              {/* Assuming you have a similar view/edit form */}
              <ViewEditAreaFacilityForm
                onClose={handleCloseViewModal}
                facilityData={selectedIdentity}
                onUpdate={handleUpdate}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AssignedIdentities;
