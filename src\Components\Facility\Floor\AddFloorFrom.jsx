import React, { useState, useEffect, useCallback, useMemo } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import Button from "../../Global/Button";
import Input from "../../Global/Input/Input";
import CustomDropdown from "../../Global/CustomDropdown";
import { toast } from "react-toastify";
import { createFloor } from "../../../api/facility";
import { useFloorMasterData } from "../../../hooks/useFloorMasterData";
import { useBuildingData } from "../../../hooks/useBuildingData";
import { sanitizeRequest } from "../../../utils/helpers";

const AddFloorForm = ({ facility, onClose, refreshFloors }) => {
  // Use custom hook to fetch and cache building data for the given facility
  const buildings = useBuildingData(facility.facility_id);

  // Get master data for floor status and occupancy type
  const { statusOptions, occupancyOptions } = useFloorMasterData();

  // Define fields for floor form
  const floorFields = [
    {
      label: "Building",
      type: "customDropdown",
      placeholder: "Select Building",
      name: "building_id",
      options: buildings,
    },
    {
      label: "Floor Number *",
      type: "number",
      placeholder: "Floor Number",
      name: "floor_number",
    },
    {
      label: "Status *",
      type: "customDropdown",
      placeholder: "Select Status",
      name: "status",
      options: statusOptions,
    },
    {
      label: "Total Square Footage *",
      type: "number",
      placeholder: "Total Square Footage",
      name: "total_square_footage",
    },
    {
      label: "Max Occupancy *",
      type: "number",
      placeholder: "Max Occupancy",
      name: "max_occupancy",
    },
    {
      label: "Occupancy Type *",
      type: "customDropdown",
      placeholder: "Select Occupancy Type",
      name: "occupancy_type",
      options: occupancyOptions,
    },
  ];

  // Build Yup validation schema using the fetched options
  const floorSchema = useMemo(
    () =>
      yup.object().shape({
        building_id: yup
          .string()
          .nullable("Building is required"),
          // .test("is-valid-building", "Invalid Building", (value) => {
          //   if (buildings && buildings.length > 0) {
          //     return buildings.map((b) => b.value).includes(value);
          //   }
          //   return true;
          // }),
        floor_number: yup.string().required("Floor Number is required"),
        status: yup
          .number()
          .required("Status is required")
          .oneOf(
            statusOptions.map((opt) => Number(opt.value)),
            "Invalid Status"
          ),
        total_square_footage: yup
          .number()
          .typeError("Total Square Footage must be a number")
          .required("Total Square Footage is required"),
        max_occupancy: yup
          .number()
          .typeError("Max Occupancy must be a number")
          .required("Max Occupancy is required"),
        occupancy_type: yup
          .number()
          .required("Occupancy Type is required")
          .oneOf(
            occupancyOptions.map((opt) => Number(opt.value)),
            "Invalid Occupancy Type"
          ),
      }),
    [buildings, statusOptions, occupancyOptions]
  );

  const {
    register,
    handleSubmit,
    control,
    setError,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(floorSchema),
    defaultValues: {
      building_id: "", // No default selection; user must choose.
      floor_number: "",
      status:"",
      total_square_footage: "",
      max_occupancy: "",
      occupancy_type:  "",
    },
  });

  const [loading, setLoading] = useState(false);

  const submitFormHandler = async (data) => {
    setLoading(true);
    try {
      const payload = sanitizeRequest(data);
      const response = await createFloor(facility.facility_id, payload);
      if (response && response.status === false) {
        throw { response: { data: { data: response.data || {} } } };
      }
      toast.success("Floor added successfully!");
      refreshFloors();
      onClose();
    } catch (error) {
      console.log(error);
      toast.error(
        error.response && error.response.data
          ? error.response.data.message
          : "Error adding buildfloorng!"
      );
      if (error.response && error.response.data && error.response.data.data) {
        const errorsData = error.response.data.data;
        Object.keys(errorsData).forEach((field) => {
          setError(field, { type: "server", message: errorsData[field] });
        });
      }
    } finally {
      setLoading(false);
    }
  };

  // Animation state
  const [show, setShow] = useState(false);

  // Mount/unmount logic for smooth open/close
  useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-white w-full h-full max-w-4xl p-6 overflow-y-auto shadow-xl transform transition-transform duration-700 ease-in-out ${
          show ? "translate-x-0" : "translate-x-full"
        }`}
        style={{ willChange: "transform" }}
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-[30px] font-normal text-[#4F2683]">Add Floor</h2>
          <button
            type="button"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
          >
            &times;
          </button>
        </div>
        <hr className="mb-6" />

        {/* Form */}
        <form
          onSubmit={handleSubmit(submitFormHandler)}
          className="bg-white rounded-lg"
        >
          <h2 className="text-[20px] text-[#333333] font-medium pb-4">Floor Details</h2>

          {floorFields.map(({ label, type, name, options, placeholder }, idx) => (
            <div key={idx} className="flex items-center mb-4">
              <label className="w-1/3 text-[16px] font-normal text-[#333333]">{label}</label>
              <div className="w-2/3">
                {type === "customDropdown" ? (
                  <Controller
                    control={control}
                    name={name}
                    defaultValue={null}
                    render={({ field }) => (
                      <CustomDropdown
                        value={field.value}
                        options={options}
                        placeholder={placeholder}
                        onSelect={(option) => {
                          const value = typeof option === "object" ? option.value : option;
                          field.onChange(value);
                        }}
                        bgColor="bg-white text-black"
                        textColor="text-black"
                        hoverBgColor="hover:bg-[#4F2683]"
                        borderColor="border-gray-300"
                        className="p-2 border h-11 rounded focus:outline-none focus:ring-1"
                        rounded="rounded"
                        error={errors[name]}
                      />
                    )}
                  />
                ) : (
                  <Input
                    type={type}
                    name={name}
                    placeholder={placeholder}
                    error={errors[name]}
                    className="w-full p-2 border rounded hide-number-spin"
                    {...register(name)}
                  />
                )}
                {errors[name] && (
                  <p className="text-red-500 text-sm mt-1">{errors[name].message}</p>
                )}
              </div>
            </div>
          ))}

          {/* Buttons */}
          <div className="flex justify-end gap-4 mt-6">
            <Button type="cancel" label="Cancel" onClick={onClose} />
            <Button
              type="primary"
              label={loading ? "Saving..." : "Add"}
              disabled={loading}
            />
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddFloorForm;
