import React, { useState } from "react";
import Button from "../Global/Button";

const OwnersAdd = ({ onSubmit, onClose, availableOwners }) => {
  const defaultOwners = [
    { name: "<PERSON>", eid: "E001", type: "Employee", organization: "Org1", jobTitle: "Developer", status: "Active" },
    { name: "<PERSON>", eid: "E002", type: "Employee", organization: "Org1", jobTitle: "Designer", status: "Inactive" },
    { name: "<PERSON>", eid: "E003", type: "Manager", organization: "Org2", jobTitle: "Project Manager", status: "Active" },
    { name: "<PERSON>", eid: "E004", type: "Employee", organization: "Org2", jobTitle: "QA Engineer", status: "Active" },
    { name: "<PERSON>", eid: "E005", type: "Employee", organization: "Org3", jobTitle: "Support", status: "Inactive" },
    { name: "<PERSON>", eid: "E006", type: "Employee", organization: "Org4", jobTitle: "Analyst", status: "Active" },
    { name: "<PERSON>", eid: "E007", type: "Manager", organization: "Org4", jobTitle: "Team Lead", status: "Active" },
    { name: "Fiona Blue", eid: "E008", type: "Employee", organization: "Org5", jobTitle: "Consultant", status: "Active" },
  ];

  const ownersList = availableOwners && availableOwners.length ? availableOwners : defaultOwners;
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedOwner, setSelectedOwner] = useState("");
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);

  // Filter owners based on the search term
  const filteredOwners = ownersList.filter((owner) =>
    owner.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelectOwner = (ownerName) => {
    setSelectedOwner(ownerName);
    setSearchTerm(ownerName);
    setIsDropdownVisible(false);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!selectedOwner) {
      alert("Please select an owner.");
      return;
    }
    const selectedOwnerObj = ownersList.find((owner) => owner.name === selectedOwner);
    onSubmit(selectedOwnerObj);
    onClose();
  };

  return (
    <div className="w-full p-4">
      {/* Header */}
      <div className="flex items-center mb-2 justify-between px-2">
        <h2 className="text-2xl font-bold text-[#4F2683]">Add Owner / Employee</h2>
        <button
          onClick={onClose}
          type="button"
          className="w-8 h-8 bg-[#4F2683] text-white flex items-center justify-center rounded-full text-2xl"
        >
          &times;
        </button>
      </div>
      <hr className="mx-3" />

      {/* Form */}
      <form onSubmit={handleSubmit} className="bg-white p-6 pt-2 rounded-lg my-3">
        {/* Search Input with Dropdown */}
        <div className="mb-4 flex items-center">
          <label className="text-[16px] font-normal w-1/4">Select Owner</label>
          <div className="relative w-3/4">
            <input
              type="text"
              placeholder="Search Owner"
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setIsDropdownVisible(true);
              }}
              onFocus={() => setIsDropdownVisible(true)}
              onBlur={() => setTimeout(() => setIsDropdownVisible(false), 150)}
              className="w-full h-11 border border-gray-300 rounded px-3"
            />
            {isDropdownVisible && (
              <div className="absolute top-full left-0 w-full mt-1 border bg-white rounded-md shadow-lg max-h-60 overflow-y-auto z-50">
                {filteredOwners.length > 0 ? (
                  filteredOwners.map((owner) => (
                    <div
                      key={owner.name}
                      className="p-2 cursor-pointer hover:bg-gray-100"
                      onMouseDown={() => handleSelectOwner(owner.name)}
                    >
                      {owner.name}
                    </div>
                  ))
                ) : (
                  <div className="p-2 text-gray-700 text-center">No Results Found.</div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-4 justify-center">
          <Button type="button" label="Cancel" onClick={onClose} className="bg-gray-400 text-white" />
          <Button type="submit" label="Add" className="bg-[#4F2683] text-white" />
        </div>
      </form>
    </div>
  );
};

export default OwnersAdd;
