import React, { useState } from "react";
import Button from "../Global/Button";
import Input from "../Global/Input/Input";
import CustomDropdown from "../Global/CustomDropdown";
import { updateGuest } from "../../api/guest";
import { toast } from "react-toastify";

const EditGuestForm = ({ guest, onClose, onSuccess }) => {
  const [formData, setFormData] = useState({
    ...guest,
    firstName: guest?.firstName || guest?.name?.split(" ")[0] || "", // Extract first name
    lastName: guest?.lastName || guest?.name?.split(" ").slice(1).join(" ") || "", // Extract last name
  });
  const [loading, setLoading] = useState(false);
  const [show, setShow] = useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Basic validation
    if (!formData.firstName.trim() || !formData.lastName.trim() || !formData.email.trim()) {
      toast.error("Please fill in all required fields (First Name, Last Name, and Email).");
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email.trim())) {
      toast.error("Please enter a valid email address.");
      return;
    }

    // Guest ID validation
    const guestId = guest?.id || guest?.guest_id || guest?.guestId || guest?._id;

    if (!guestId) {
      toast.error("Cannot find guest ID. Please refresh the page and try again.");
      return;
    }

    // GUID validation (optional - check if it's a valid GUID format)
    const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!guidRegex.test(guestId)) {
      console.warn("🔍 Guest ID is not in GUID format:", guestId, "Type:", typeof guestId);
      // Don't return here - let the API handle it
    }

    setLoading(true);
    try {
      const guestData = {
        // ✅ Remove guest_id from body - API doesn't allow it in request body
        first_name: formData.firstName.trim(),
        last_name: formData.lastName.trim(),
        email: formData.email.trim(),
        mobile_phone: formData.mobilePhone?.trim() || null,
        company: formData.company?.trim() || null,
        private_visitor: formData.isPrivate === "Yes",
      };



      try {
        await updateGuest(guestId, guestData);
        toast.success("Guest updated successfully!");
      } catch (apiError) {
        console.error("API Error:", apiError);
        console.error("API Error Response:", apiError.response?.data);

        if (apiError.response?.status === 401) {
          toast.error("Please login to update guests.");
        } else if (apiError.response?.status === 400) {
          const errorMessage = apiError.response?.data?.message || "Invalid guest data. Please check your inputs.";
          toast.error(errorMessage);
        } else if (apiError.response?.status === 404) {
          toast.error("Guest not found.");
        } else if (apiError.response?.status === 409) {
          toast.error("A guest with this email already exists.");
        } else {
          const errorMessage = apiError.response?.data?.message || "Failed to update guest";
          toast.error(errorMessage);
        }
      }
      onSuccess();
    } catch (error) {
      console.error("Error updating guest:", error);
      toast.error("Failed to update guest. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`w-full max-w-3xl bg-white rounded-lg shadow-lg h-full p-0 transform transition-transform duration-700 ease-in-out ${
          show ? "translate-x-0" : "translate-x-full"
        }`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center mb-2 px-4 pt-2 justify-between">
          <h2 className="text-[30px] font-normal text-[#4F2683]">Edit Guest</h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
          >
            &times;
          </button>
        </div>
        <hr className="mx-3" />
        <form
          onSubmit={handleSubmit}
          className="bg-white p-6 rounded-lg shadow-lg"
        >
          <h2 className="text-[20px] text-[#333333] font-medium pb-4">
            Guest Details
          </h2>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              First Name *
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                name="firstName"
                placeholder="First Name"
                value={formData.firstName}
                onChange={handleChange}
                className="hide-number-spin"
                required
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Last Name *
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                name="lastName"
                placeholder="Last Name"
                value={formData.lastName}
                onChange={handleChange}
                className="hide-number-spin"
                required
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Email *
            </label>
            <div className="w-3/4">
              <Input
                type="email"
                name="email"
                placeholder="Email"
                value={formData.email}
                onChange={handleChange}
                className="hide-number-spin"
                required
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Mobile Phone
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                name="mobilePhone"
                placeholder="Mobile Phone"
                value={formData.mobilePhone}
                onChange={handleChange}
                className="hide-number-spin"
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Company
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                name="company"
                placeholder="Company"
                value={formData.company}
                onChange={handleChange}
                className="hide-number-spin"
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Private Visitor?
            </label>
            <div className="w-3/4">
              <CustomDropdown
                options={[
                  { label: "Yes", value: "Yes" },
                  { label: "No", value: "No" },
                ]}
                value={formData.isPrivate}
                onSelect={(value) => setFormData({ ...formData, isPrivate: value })}
                placeholder="Select Option"
                className="h-11"
              />
            </div>
          </div>

          <div className="flex justify-center gap-4 mt-6">
            <Button type="cancel" label="Cancel" onClick={onClose} disabled={loading} />
            <Button buttonType="submit" type="primary" label={loading ? "Saving..." : "Save"} disabled={loading} />
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditGuestForm;





