import React, { useState, useEffect } from "react";
import Input from "../../Components/Global/Input/Input";
import CustomDropdown from "../../Components/Global/CustomDropdown";

const ValidationViewEditConfiguration = ({
  configurationData,
  onUpdate,
  onClose,
  startInEditMode = false,
}) => {
  // Initialize formData from configurationData and start in edit mode if required
  const [formData, setFormData] = useState({ ...configurationData });
  const [isEditing, setIsEditing] = useState(startInEditMode);
  const [currentStep, setCurrentStep] = useState(1);
  const [show, setShow] = useState(false);

  // Reset local state when configurationData or startInEditMode changes
  useEffect(() => {
    setFormData({ ...configurationData });
    setIsEditing(startInEditMode);
    setCurrentStep(1);
  }, [configurationData, startInEditMode]);

  useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  // Handle text input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle dropdown changes
  const handleDropdownChange = (fieldName, value) => {
    setFormData((prev) => ({ ...prev, [fieldName]: value }));
  };

  // Save and exit edit mode
  const handleSave = (e) => {
    e.preventDefault();
    if (onUpdate) onUpdate(formData);
    setIsEditing(false);
    setShow(false);
    setTimeout(onClose, 700);
  };

  // Cancel editing and reset to original data
  const handleCancel = () => {
    setFormData({ ...configurationData });
    setIsEditing(false);
    setCurrentStep(1);
    setShow(false);
    setTimeout(onClose, 700);
  };

  return (
    <div className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50 transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`} style={{ willChange: "transform" }}>
      <div className="w-full max-w-4xl bg-white rounded-lg shadow-lg h-full p-0 overflow-y-auto">
        <div className="w-full p-4">
          {/* Header */}
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-[30px] font-normal text-[#4F2683]">
              View/Edit Validation Configuration
            </h2>
            <button
              type="button"
              className="w-8 h-8 bg-[#4F2683] text-white rounded-full text-2xl flex items-center justify-center"
              onClick={() => {
                setShow(false);
                setTimeout(onClose, 700);
              }}
            >
              &times;
            </button>
          </div>

          {/* Tabs */}
          <div className="flex items-center mt-4 space-x-8">
            <button
              type="button"
              onClick={() => setCurrentStep(1)}
              className={`p-2 border rounded-full ${
                currentStep === 1
                  ? "bg-[#4F2683] border-[#4F2683] text-white"
                  : "bg-transparent border-[#4F2683] text-gray-500"
              }`}
            >
              Configuration
            </button>
            <button
              type="button"
              onClick={() => setCurrentStep(2)}
              className={`p-2 border rounded-full ${
                currentStep === 2
                  ? "bg-[#4F2683] border-[#4F2683] text-white"
                  : "bg-transparent border-[#4F2683] text-gray-500"
              }`}
            >
              Messaging
            </button>
          </div>

          {/* Form Content */}
          <form onSubmit={handleSave} className="bg-white p-6 rounded-lg mt-4">
            {currentStep === 1 && (
              <div className="flex flex-col gap-4">
                {/* Title */}
                <div className="flex items-center">
                  <label htmlFor="title" className="w-1/3">
                    Title
                  </label>
                  <div className="w-2/3">
                    <Input
                      type="text"
                      name="title"
                      id="title"
                      value={formData.title || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Title"
                      disabled={!isEditing}
                    />
                  </div>
                </div>
                {/* Description */}
                <div className="flex items-center">
                  <label htmlFor="description" className="w-1/3">
                    Description
                  </label>
                  <div className="w-2/3">
                    <Input
                      type="text"
                      name="description"
                      id="description"
                      value={formData.description || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Description"
                      disabled={!isEditing}
                    />
                  </div>
                </div>
                {/* Validation Status */}
                <div className="flex items-center">
                  <label htmlFor="status" className="w-1/3">
                    Validation Status
                  </label>
                  <div className="w-2/3">
                    {isEditing ? (
                      <CustomDropdown
                        options={["Active", "Pending"]}
                        selectedOption={formData.status}
                        value={formData.status}
                        onSelect={(value) => handleDropdownChange("status", value)}
                        placeholder="Select Status"
                        className="w-full h-11"
                        hoverBgColor="hover:bg-[#4F2683]"
                      />
                    ) : (
                      <Input
                        type="text"
                        name="status"
                        id="status"
                        value={formData.status || ""}
                        disabled
                        className="w-full h-11"
                      />
                    )}
                  </div>
                </div>
                {/* Validation Type */}
                <div className="flex items-center">
                  <label htmlFor="type" className="w-1/3">
                    Validation Type
                  </label>
                  <div className="w-2/3">
                    {isEditing ? (
                      <CustomDropdown
                        options={["Access Area", "Area Owner", "Another"]}
                        selectedOption={formData.type}
                        value={formData.type}
                        onSelect={(value) => handleDropdownChange("type", value)}
                        placeholder="Select Type"
                        className="w-full h-11"
                        hoverBgColor="hover:bg-[#4F2683]"
                      />
                    ) : (
                      <Input
                        type="text"
                        name="type"
                        id="type"
                        value={formData.type || ""}
                        disabled
                        className="w-full h-11"
                      />
                    )}
                  </div>
                </div>
                {/* Assignee */}
                <div className="flex items-center">
                  <label htmlFor="owner" className="w-1/3">
                    Assignee
                  </label>
                  <div className="w-2/3">
                    {isEditing ? (
                      <CustomDropdown
                        options={["Owner Only", "User", "Another"]}
                        selectedOption={formData.owner}
                        value={formData.owner}
                        onSelect={(value) => handleDropdownChange("owner", value)}
                        placeholder="Select Assignee"
                        className="w-full h-11"
                        hoverBgColor="hover:bg-[#4F2683]"
                      />
                    ) : (
                      <Input
                        type="text"
                        name="owner"
                        id="owner"
                        value={formData.owner || ""}
                        disabled
                        className="w-full h-11"
                      />
                    )}
                  </div>
                </div>
                {/* Start Date */}
                <div className="flex items-center">
                  <label htmlFor="startDate" className="w-1/3">
                    Start Date
                  </label>
                  <div className="w-2/3">
                    <Input
                      type="date"
                      name="startDate"
                      id="startDate"
                      value={formData.startDate || ""}
                      onChange={handleInputChange}
                      disabled={!isEditing}
                    />
                  </div>
                </div>
                {/* Recurrence */}
                <div className="flex items-center">
                  <label htmlFor="reccurence" className="w-1/3">
                    Recurrence
                  </label>
                  <div className="w-2/3">
                    {isEditing ? (
                      <CustomDropdown
                        options={["Every Month", "Every Quarter", "Every Year"]}
                        selectedOption={formData.reccurence}
                        value={formData.reccurence}
                        onSelect={(value) => handleDropdownChange("reccurence", value)}
                        placeholder="Select Recurrence"
                        className="w-full h-11"
                        hoverBgColor="hover:bg-[#4F2683]"
                      />
                    ) : (
                      <Input
                        type="text"
                        name="reccurence"
                        id="reccurence"
                        value={formData.reccurence || ""}
                        disabled
                        className="w-full h-11"
                      />
                    )}
                  </div>
                </div>
                {/* Record */}
                <div className="flex items-center">
                  <label htmlFor="record" className="w-1/3">
                    Record
                  </label>
                  <div className="w-2/3">
                    <Input
                      type="text"
                      name="record"
                      id="record"
                      value={formData.record || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Record"
                      disabled={!isEditing}
                    />
                  </div>
                </div>
              </div>
            )}

            {currentStep === 2 && (
              <div className="flex flex-col gap-4">
                {/* Closes On */}
                <div className="flex items-center">
                  <label htmlFor="closesOn" className="w-1/3">
                    Closes On
                  </label>
                  <div className="w-2/3">
                    <Input
                      type="date"
                      name="closesOn"
                      id="closesOn"
                      value={formData.closesOn || ""}
                      onChange={handleInputChange}
                      disabled={!isEditing}
                    />
                  </div>
                </div>
                {/* Validation Reminder and checkboxes */}
                <div className="flex space-x-4 mt-2">
                  <label className="flex items-center space-x-2">
                    <input type="checkbox" className="form-checkbox" />
                    <span>Retain All</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input type="checkbox" className="form-checkbox" />
                    <span>Revoke All</span>
                  </label>
                </div>
                <h2 className="text-[20px] font-normal">Notify Reviewer</h2>
                <div className="flex items-center">
                  <label htmlFor="validationReminder" className="w-1/3">
                    Validation Reminder
                  </label>
                  <div className="w-2/3">
                    <Input
                      type="text"
                      name="validationReminder"
                      id="validationReminder"
                      value={formData.validationReminder || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Validation Reminder"
                      disabled={!isEditing}
                    />
                  </div>
                </div>
                {/* Template */}
                <div className="flex items-center">
                  <label htmlFor="template" className="w-1/3">
                    Template
                  </label>
                  <div className="w-2/3">
                    {isEditing ? (
                      <CustomDropdown
                        options={["Template 1", "Template 2", "Template 3"]}
                        selectedOption={formData.template}
                        value={formData.template}
                        onSelect={(value) => handleDropdownChange("template", value)}
                        placeholder="Select Template"
                        className="w-full h-11"
                        hoverBgColor="hover:bg-[#4F2683]"
                      />
                    ) : (
                      <Input
                        type="text"
                        name="template"
                        id="template"
                        value={formData.template || ""}
                        disabled
                        className="w-full h-11"
                      />
                    )}
                  </div>
                </div>
                {/* To */}
                <div className="flex items-center">
                  <label htmlFor="to" className="w-1/3">
                    To
                  </label>
                  <div className="w-2/3">
                    {isEditing ? (
                      <CustomDropdown
                        options={["User 1", "User 2", "User 3"]}
                        selectedOption={formData.to}
                        value={formData.to}
                        onSelect={(value) => handleDropdownChange("to", value)}
                        placeholder="Select Recipient"
                        className="w-full h-11"
                        hoverBgColor="hover:bg-[#4F2683]"
                      />
                    ) : (
                      <Input
                        type="text"
                        name="to"
                        id="to"
                        value={formData.to || ""}
                        disabled
                        className="w-full h-11"
                      />
                    )}
                  </div>
                </div>
                {/* CC */}
                <div className="flex items-center">
                  <label htmlFor="cc" className="w-1/3">
                    CC
                  </label>
                  <div className="w-2/3">
                    {isEditing ? (
                      <CustomDropdown
                        options={["User 1", "User 2", "User 3"]}
                        selectedOption={formData.cc}
                        value={formData.cc}
                        onSelect={(value) => handleDropdownChange("cc", value)}
                        placeholder="Select Option"
                        className="w-full h-11"
                        hoverBgColor="hover:bg-[#4F2683]"
                      />
                    ) : (
                      <Input
                        type="text"
                        name="cc"
                        id="cc"
                        value={formData.cc || ""}
                        disabled
                        className="w-full h-11"
                      />
                    )}
                  </div>
                </div>
                {/* Final Reminder */}
                <div className="flex items-center">
                  <label htmlFor="finalReminder" className="w-1/3">
                    Final Reminder
                  </label>
                  <div className="w-2/3">
                    <Input
                      type="text"
                      name="finalReminder"
                      id="finalReminder"
                      value={formData.finalReminder || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Final Reminder"
                      disabled={!isEditing}
                    />
                  </div>
                </div>
                {/* Template 2 */}
                <div className="flex items-center">
                  <label htmlFor="template2" className="w-1/3">
                    Template 2
                  </label>
                  <div className="w-2/3">
                    {isEditing ? (
                      <CustomDropdown
                        options={["Template 1", "Template 2", "Template 3"]}
                        selectedOption={formData.template2}
                        value={formData.template2}
                        onSelect={(value) => handleDropdownChange("template2", value)}
                        placeholder="Select Template"
                        className="w-full h-11"
                        hoverBgColor="hover:bg-[#4F2683]"
                      />
                    ) : (
                      <Input
                        type="text"
                        name="template2"
                        id="template2"
                        value={formData.template2 || ""}
                        disabled
                        className="w-full h-11"
                      />
                    )}
                  </div>
                </div>
                {/* To 2 */}
                <div className="flex items-center">
                  <label htmlFor="to2" className="w-1/3">
                    To 2
                  </label>
                  <div className="w-2/3">
                    {isEditing ? (
                      <CustomDropdown
                        options={["User 1", "User 2", "User 3"]}
                        selectedOption={formData.to2}
                        value={formData.to2}
                        onSelect={(value) => handleDropdownChange("to2", value)}
                        placeholder="Select Recipient"
                        className="w-full h-11"
                        hoverBgColor="hover:bg-[#4F2683]"
                      />
                    ) : (
                      <Input
                        type="text"
                        name="to2"
                        id="to2"
                        value={formData.to2 || ""}
                        disabled
                        className="w-full h-11"
                      />
                    )}
                  </div>
                </div>
                {/* CC 2 */}
                <div className="flex items-center">
                  <label htmlFor="cc2" className="w-1/3">
                    CC 2
                  </label>
                  <div className="w-2/3">
                    {isEditing ? (
                      <CustomDropdown
                        options={["User 1", "User 2", "User 3"]}
                        selectedOption={formData.cc2}
                        value={formData.cc2}
                        onSelect={(value) => handleDropdownChange("cc2", value)}
                        placeholder="Select Option"
                        className="w-full h-11"
                        hoverBgColor="hover:bg-[#4F2683]"
                      />
                    ) : (
                      <Input
                        type="text"
                        name="cc2"
                        id="cc2"
                        value={formData.cc2 || ""}
                        disabled
                        className="w-full h-11"
                      />
                    )}
                  </div>
                </div>
                {/* Lapse Alert */}
                <div className="flex items-center">
                  <label htmlFor="lapseAlert" className="w-1/3">
                    Lapse Alert
                  </label>
                  <div className="w-2/3">
                    <Input
                      type="text"
                      name="lapseAlert"
                      id="lapseAlert"
                      value={formData.lapseAlert || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Lapse Alert"
                      disabled={!isEditing}
                    />
                  </div>
                </div>
                {/* Template 3 */}
                <div className="flex items-center">
                  <label htmlFor="template3" className="w-1/3">
                    Template 3
                  </label>
                  <div className="w-2/3">
                    {isEditing ? (
                      <CustomDropdown
                        options={["Template 1", "Template 2", "Template 3"]}
                        selectedOption={formData.template3}
                        value={formData.template3}
                        onSelect={(value) => handleDropdownChange("template3", value)}
                        placeholder="Select Template"
                        className="w-full h-11"
                        hoverBgColor="hover:bg-[#4F2683]"
                      />
                    ) : (
                      <Input
                        type="text"
                        name="template3"
                        id="template3"
                        value={formData.template3 || ""}
                        disabled
                        className="w-full h-11"
                      />
                    )}
                  </div>
                </div>
                {/* To 3 */}
                <div className="flex items-center">
                  <label htmlFor="to3" className="w-1/3">
                    To 3
                  </label>
                  <div className="w-2/3">
                    {isEditing ? (
                      <CustomDropdown
                        options={["User 1", "User 2", "User 3"]}
                        selectedOption={formData.to3}
                        value={formData.to3}
                        onSelect={(value) => handleDropdownChange("to3", value)}
                        placeholder="Select Recipient"
                        className="w-full h-11"
                        hoverBgColor="hover:bg-[#4F2683]"
                      />
                    ) : (
                      <Input
                        type="text"
                        name="to3"
                        id="to3"
                        value={formData.to3 || ""}
                        disabled
                        className="w-full h-11"
                      />
                    )}
                  </div>
                </div>
                {/* CC 3 */}
                <div className="flex items-center">
                  <label htmlFor="cc3" className="w-1/3">
                    CC 3
                  </label>
                  <div className="w-2/3">
                    {isEditing ? (
                      <CustomDropdown
                        options={["User 1", "User 2", "User 3"]}
                        selectedOption={formData.cc3}
                        value={formData.cc3}
                        onSelect={(value) => handleDropdownChange("cc3", value)}
                        placeholder="Select Option"
                        className="w-full h-11"
                        hoverBgColor="hover:bg-[#4F2683]"
                      />
                    ) : (
                      <Input
                        type="text"
                        name="cc3"
                        id="cc3"
                        value={formData.cc3 || ""}
                        disabled
                        className="w-full h-11"
                      />
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Bottom Navigation Buttons */}
            <div className="flex justify-end gap-4 mt-6">
              {currentStep === 1 && !isEditing && (
                <>
                  <button
                    type="button"
                    onClick={() => setIsEditing(true)}
                    className="px-4 py-2 bg-[#4F2683] text-white rounded"
                  >
                    Edit
                  </button>
                  <button
                    type="button"
                    onClick={() => setCurrentStep(2)}
                    className="px-4 py-2 bg-[#4F2683] text-white rounded"
                  >
                    Next
                  </button>
                  <button
                    type="button"
                    onClick={onClose}
                    className="px-4 py-2 bg-gray-400 text-white rounded"
                  >
                    Close
                  </button>
                </>
              )}
              {currentStep === 1 && isEditing && (
                <>
                  <button
                    type="button"
                    onClick={() => setCurrentStep(2)}
                    className="px-4 py-2 bg-[#4F2683] text-white rounded"
                  >
                    Next
                  </button>
                  <button
                    type="button"
                    onClick={handleCancel}
                    className="px-4 py-2 bg-gray-400 text-white rounded"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-[#4F2683] text-white rounded"
                  >
                    Save
                  </button>
                </>
              )}
              {currentStep === 2 && !isEditing && (
                <>
                  <button
                    type="button"
                    onClick={() => setCurrentStep(1)}
                    className="px-4 py-2 bg-gray-400 text-white rounded"
                  >
                    Previous
                  </button>
                  <button
                    type="button"
                    onClick={() => setIsEditing(true)}
                    className="px-4 py-2 bg-[#4F2683] text-white rounded"
                  >
                    Edit
                  </button>
                  <button
                    type="button"
                    onClick={onClose}
                    className="px-4 py-2 bg-gray-400 text-white rounded"
                  >
                    Close
                  </button>
                </>
              )}
              {currentStep === 2 && isEditing && (
                <>
                  <button
                    type="button"
                    onClick={() => setCurrentStep(1)}
                    className="px-4 py-2 bg-gray-400 text-white rounded"
                  >
                    Previous
                  </button>
                  <button
                    type="button"
                    onClick={handleCancel}
                    className="px-4 py-2 bg-gray-400 text-white rounded"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-[#4F2683] text-white rounded"
                  >
                    Save
                  </button>
                </>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ValidationViewEditConfiguration;
