import React, { useState } from "react";
import Button from "../../Global/Button";
import Input from "../../Global/Input/Input";
import CustomDropdown from "../../Global/CustomDropdown";

const AddInterfaceStatusForm = ({ onSubmit, onClose }) => {
  const [referenceId, setReferenceId] = useState("");
  const [source, setSource] = useState("");
  const [destination, setDestination] = useState("");
  const [sentDate, setSentDate] = useState("");
  const [ackDate, setAckDate] = useState("");
  const [status, setStatus] = useState("");
  const [show, setShow] = useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const handleSubmit = (e) => {
    e.preventDefault();
    // Force status to be a string before trimming
    const trimmedReferenceId = referenceId.trim();
    const trimmedSource = source.trim();
    const trimmedDestination = destination.trim();
    const trimmedSentDate = sentDate.trim();
    const trimmedAckDate = ackDate.trim();
    const trimmedStatus = String(status).trim();

    if (
      !trimmedReferenceId ||
      !trimmedSource ||
      !trimmedDestination ||
      !trimmedSentDate ||
      !trimmedAckDate ||
      !trimmedStatus
    ) {
      alert("Please fill in all required fields.");
      return;
    }
    const newStatus = {
      id: Date.now(),
      referenceId: trimmedReferenceId,
      source: trimmedSource,
      destination: trimmedDestination,
      sentDate: trimmedSentDate,
      ackDate: trimmedAckDate,
      status: trimmedStatus,
    };

    onSubmit(newStatus);

    // Reset fields after submission
    setReferenceId("");
    setSource("");
    setDestination("");
    setSentDate("");
    setAckDate("");
    setStatus("");
  };

  return (
    <div
      className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50 transition-transform duration-700 ease-in-out ${
        show ? "translate-x-0" : "translate-x-full"
      }`}
      style={{ willChange: "transform" }}
    >
      <div className="bg-white w-full h-full max-w-3xl p-4 rounded-lg shadow-lg overflow-y-auto">
        <div className="flex items-center mb-2 px-2 justify-between">
          <h2 className="text-[30px] font-normal text-[#4F2683]">
            Add Interface Status
          </h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
          >
            &times;
          </button>
        </div>
        <hr className="mx-3" />
        <form onSubmit={handleSubmit} className="bg-white p-6 rounded-lg">
          <h2 className="text-[20px] text-[#333333] font-medium pb-4">
            Interface Status Details
          </h2>

          {/* Reference ID */}
          <div className="flex items-center mb-4">
            <label
              htmlFor="referenceId"
              className="w-1/4 text-[16px] font-normal text-[#333333]"
            >
              Reference ID*
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                id="referenceId"
                placeholder="Reference ID"
                value={referenceId}
                onChange={(e) => setReferenceId(e.target.value)}
                required
                className="w-full border border-gray-300 rounded p-2"
              />
            </div>
          </div>

          {/* Source */}
          <div className="flex items-center mb-4">
            <label
              htmlFor="source"
              className="w-1/4 text-[16px] font-normal text-[#333333]"
            >
              Source*
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                id="source"
                placeholder="Source"
                value={source}
                onChange={(e) => setSource(e.target.value)}
                required
                className="w-full border border-gray-300 rounded p-2"
              />
            </div>
          </div>

          {/* Destination */}
          <div className="flex items-center mb-4">
            <label
              htmlFor="destination"
              className="w-1/4 text-[16px] font-normal text-[#333333]"
            >
              Destination*
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                id="destination"
                placeholder="Destination"
                value={destination}
                onChange={(e) => setDestination(e.target.value)}
                required
                className="w-full border border-gray-300 rounded p-2"
              />
            </div>
          </div>

          {/* Sent Date */}
          <div className="flex items-center mb-4">
            <label
              htmlFor="sentDate"
              className="w-1/4 text-[16px] font-normal text-[#333333]"
            >
              Sent Date*
            </label>
            <div className="w-3/4">
              <Input
                type="date"
                id="sentDate"
                placeholder="Sent Date"
                value={sentDate}
                onChange={(e) => setSentDate(e.target.value)}
                required
                className="w-full border border-gray-300 rounded p-2"
              />
            </div>
          </div>

          {/* Ack Date */}
          <div className="flex items-center mb-4">
            <label
              htmlFor="ackDate"
              className="w-1/4 text-[16px] font-normal text-[#333333]"
            >
              Ack Date*
            </label>
            <div className="w-3/4">
              <Input
                type="date"
                id="ackDate"
                placeholder="Ack Date"
                value={ackDate}
                onChange={(e) => setAckDate(e.target.value)}
                required
                className="w-full border border-gray-300 rounded p-2"
              />
            </div>
          </div>

          {/* Status */}
          <div className="flex items-center mb-4">
            <label
              htmlFor="status"
              className="w-1/4 text-[16px] font-normal text-[#333333]"
            >
              Status*
            </label>
            <div className="w-3/4">
              <CustomDropdown
                className="h-11 rounded"
                options={["Active", "Inactive"]}
                placeholder="Status"
                onSelect={(option) => setStatus(option)}
                selectedOption={status}
                value={status}
                hoverBgColor="hover:bg-[#4F2683]"
                borderColor="border-gray-300"
              />
            </div>
          </div>

          <div className="flex gap-4 pb-4 justify-center">
            <Button
              type="cancel"
              label="Cancel"
              onClick={onClose}
              className="bg-gray-400 text-white"
            />
            <Button type="primary" label="Add" className="text-white" />
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddInterfaceStatusForm;
