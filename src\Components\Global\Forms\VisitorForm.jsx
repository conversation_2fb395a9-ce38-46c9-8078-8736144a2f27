import React, { useRef } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import Button from "../Button";
import ImageCapture from "../ImageAndCamera/ImageCaptureForForm";
import Input from "../Input/Input";
import DateInput from "../Input/DateInput";
import CustomDropdown from "../CustomDropdown";
import demo from "../../../Images/fromimg.svg";

const VisitorForm = ({ fieldsToRender, onAddGuest, onClose, hostName }) => {
  const defaultFields = [
    "facility",
    "escortName",
    "hostName",
    "startDate",
    "startTime",
    "endTime",
    "visitorName",
    "dob",
    "guestMail",
    "phoneNumber",
    "relationship",
  ];
  fieldsToRender = fieldsToRender || defaultFields;

  const submitActionRef = useRef("");
  const facilityOptions = ["xray-room", "operation-theater"];
  const durationOptions = ["2:00 Hour", "4:00 Hour", "8:00 Hour", "12:00 Hour"];

  // Set startTime in 24-hour format
  const currentTime = new Date().toLocaleTimeString([], {
    hour12: false,
    hour: "2-digit",
    minute: "2-digit",
  });

  const initialValues = {
    // Patient Information
    facility: "",
    hostName: hostName || "",
    startDate: new Date(),
    startTime: currentTime,
    endTime: "4:00 Hour", // Default duration is set here
    // Guest Information
    visitorName: "",
    dob: "",
    guestMail: "",
    phoneNumber: "",
    relationship: "",
    image: "",
  };

  const validationSchema = Yup.object({
    visitorName: Yup.string().required("Guest Name is required"),
  });

  const handleSubmit = (values, { resetForm }) => {
    const durationMap = {
      "2:00 Hour": 2,
      "4:00 Hour": 4,
      "8:00 Hour": 8,
      "12:00 Hour": 12,
    };
    const durationHours = durationMap[values.endTime] || 0;

    // Ensure startDate is in ISO date format (YYYY-MM-DD)
    const dateStr =
      typeof values.startDate === "string"
        ? values.startDate
        : values.startDate.toISOString().split("T")[0];

    // Combine date and time to form a valid Date object
    const startDateTime = new Date(`${dateStr}T${values.startTime}:00`);
    console.log("Start DateTime:", startDateTime);

    // Add the selected duration hours to calculate endDateTime
    const endDateTime = new Date(startDateTime);
    endDateTime.setHours(endDateTime.getHours() + durationHours);
    console.log("End DateTime:", endDateTime);

    const newGuest = {
      visitorName: values.visitorName,
      dob: values.dob,
      guestMail: values.guestMail,
      phoneNumber: values.phoneNumber,
      relationship: values.relationship,
      screening: true,
      startDate: `${startDateTime.toLocaleDateString()} ${startDateTime.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      })}`,
      endDate: `${endDateTime.toLocaleDateString()} ${endDateTime.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      })}`,
      image: values.image || demo,
    };

    onAddGuest(newGuest);

    if (submitActionRef.current === "addMore") {
      resetForm({
        values: {
          ...values,
          visitorName: "",
          dob: "",
          guestMail: "",
          phoneNumber: "",
          relationship: "",
          image: "",
        },
        errors: {},
        touched: {},
      });
    } else if (submitActionRef.current === "save") {
      resetForm();
      onClose();
    }
  };

  const patientFields = ["facility", "hostName", "startDate", "startTime", "endTime"];
  const hasPatientFields = fieldsToRender.some((field) => patientFields.includes(field));

  return (
    <div className="px-12 py-6 mb-8 border rounded-md shadow-[0px_3.941415548324585px_7.88283109664917px_4.926765441894531px_rgba(79,38,131,0.06)]">
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        validateOnMount={false}
      >
        {({ setFieldValue, values, submitForm, touched, errors, handleBlur }) => (
          <Form className="space-y-4">
            {/* Patient Information */}
            {hasPatientFields && (
              <>
                <div className="flex justify-between">
                  <h1 className="text-xl md:text-lg font-normal text-[#4F2683]">
                    Create Walk In Visit
                  </h1>
                  <button
                    type="button"
                    className="text-xl text-white rounded-full h-8 w-8 bg-[#4F2683]"
                    onClick={onClose}
                  >
                    &times;
                  </button>
                </div>
                <div className="bg-gray-100 p-4 py-6 rounded-lg border rounded-tr-3xl border-[#4F2683]">
                  <div className="flex gap-4 w-full">
                    {fieldsToRender.includes("facility") && (
                      <div className="w-1/4">
                        <h2>Facility</h2>
                        <CustomDropdown
                          options={facilityOptions}
                          bgColor="bg-[white] text-black"
                          textColor="text-black"
                          hoverBgColor="hover:bg-[#4F2683]"
                          borderColor="border-gray-300"
                          rounded="rounded-[6px]"
                          className="border h-11 p-2 focus:outline-none focus:ring-1"
                        />
                      </div>
                    )}
                    {fieldsToRender.includes("hostName") && (
                      <div className="w-1/4">
                        <Input
                          type="text"
                          label="Host Name*"
                          name="hostName"
                          value={values.hostName}
                          className="border h-11 p-2 focus:outline-none rounded-[6px] focus:ring-1"
                          placeholder="Enter Escort Name"
                          onChange={(e) => setFieldValue("hostName", e.target.value)}
                          onBlur={handleBlur}
                          disabled={!!hostName}
                        />
                      </div>
                    )}
                    {fieldsToRender.includes("escortName") && (
                      <div className="w-1/4">
                        <Input
                          type="text"
                          label="Escort Name"
                          name="escortName"
                          value={values.escortName}
                          className="border h-11 p-2 focus:outline-none rounded-[6px] focus:ring-1"
                          placeholder="Enter Escort Name"
                          onChange={(e) => setFieldValue("escortName", e.target.value)}
                          onBlur={handleBlur}
                        />
                      </div>
                    )}
                    {fieldsToRender.includes("startDate") && (
                      <div className="w-1/4">
                        <DateInput
                          label="Start Date"
                          name="startDate"
                          value={values.startDate}
                          onChange={(date) => setFieldValue("startDate", date)}
                          placeholder="MM-DD-YYYY"
                          onBlur={handleBlur}
                        />
                      </div>
                    )}
                    {fieldsToRender.includes("startTime") && (
                      <div className="w-1/4">
                        <Input
                          type="time"
                          label="Start Time"
                          name="startTime"
                          value={values.startTime}
                          onChange={(e) => setFieldValue("startTime", e.target.value)}
                          onBlur={handleBlur}
                        />
                      </div>
                    )}
                    {fieldsToRender.includes("endTime") && (
                      <div className="w-1/4">
                        <h2>Duration</h2>
                        <CustomDropdown
                          options={durationOptions}
                          defaultValue="4:00 Hour"
                          bgColor="bg-[white] text-black"
                          textColor="text-black"
                          hoverBgColor="hover:bg-[#4F2683]"
                          borderColor="border-gray-300"
                          rounded="rounded-[6px]"
                          className="border h-11 p-2 focus:outline-none focus:ring-1"
                          onChange={(value) => {
                            console.log("Selected Duration:", value);
                            setFieldValue("endTime", value);
                          }}
                        />
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}

            {/* Guest Information */}
            <div className="p-4">
              <div className="flex justify-between mb-4">
                {!hasPatientFields && (
                  <button
                    type="button"
                    className="text-xl text-white rounded-full h-8 w-8 bg-[#4F2683]"
                    onClick={onClose}
                  >
                    &times;
                  </button>
                )}
              </div>
              <div>
                <ImageCapture
                  onImageCaptured={(img) => setFieldValue("image", img)}
                  onImageUploaded={(img) => setFieldValue("image", img)}
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-16">
                {fieldsToRender.includes("visitorName") && (
                  <div className="w-full">
                    <Input
                      type="text"
                      label="Guest Name*"
                      name="visitorName"
                      value={values.visitorName}
                      placeholder="Enter Guest Name"
                      onChange={(e) => setFieldValue("visitorName", e.target.value)}
                      onBlur={handleBlur}
                    />
                    {touched.visitorName && errors.visitorName && (
                      <div className="text-red-500 text-sm">{errors.visitorName}</div>
                    )}
                  </div>
                )}
                {fieldsToRender.includes("dob") && (
                  <div className="w-full">
                    <DateInput
                      label="Guest DOB"
                      name="dob"
                      value={values.dob}
                      className="h-11"
                      onChange={(date) => setFieldValue("dob", date)}
                      placeholder="Select a date"
                      onBlur={handleBlur}
                    />
                  </div>
                )}
                {fieldsToRender.includes("guestMail") && (
                  <div className="w-full">
                    <Input
                      type="email"
                      label="Guest Mail"
                      name="guestMail"
                      value={values.guestMail}
                      placeholder="Enter Guest Email"
                      onChange={(e) => setFieldValue("guestMail", e.target.value)}
                      onBlur={handleBlur}
                    />
                  </div>
                )}
                {fieldsToRender.includes("phoneNumber") && (
                  <div className="w-full">
                    <Input
                      type="tel"
                      label="Phone Number"
                      name="phoneNumber"
                      value={values.phoneNumber}
                      placeholder="Enter Phone Number"
                      onChange={(e) => setFieldValue("phoneNumber", e.target.value)}
                      onBlur={handleBlur}
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Submission Buttons */}
            <div className="flex gap-4 pl-4 justify-center pb-4">
              <Button
                buttonType="button"
                type="primary"
                onClick={() => {
                  submitActionRef.current = "addMore";
                  submitForm();
                  console.log("Add More");
                }}
                label="Add More"
              />
              <Button
                type="primary"
                onClick={() => {
                  submitActionRef.current = "save";
                  submitForm();
                }}
                label="Save"
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default VisitorForm;
