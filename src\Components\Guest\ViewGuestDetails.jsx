import React, { useState } from "react";

const ViewGuestDetails = ({ guest, onClose }) => {
  const [show, setShow] = useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`w-full max-w-3xl bg-white rounded-lg shadow-lg h-full p-0 transform transition-transform duration-700 ease-in-out ${
          show ? "translate-x-0" : "translate-x-full"
        }`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center mb-2 px-4 pt-2 justify-between">
          <h2 className="text-[30px] font-normal text-[#4F2683]">Guest Details</h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
          >
            &times;
          </button>
        </div>
        <hr className="mx-3" />
        <div className="bg-white p-6 rounded-lg">
          <h2 className="text-[20px] text-[#333333] font-medium pb-4">
            Guest Information
          </h2>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              First Name
            </label>
            <div className="w-3/4">
              <p className="p-2 border h-11 rounded bg-gray-100">
                {guest.firstName || guest.name?.split(" ")[0] || "N/A"}
              </p>
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Last Name
            </label>
            <div className="w-3/4">
              <p className="p-2 border h-11 rounded bg-gray-100">
                {guest.lastName || guest.name?.split(" ").slice(1).join(" ") || "N/A"}
              </p>
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Email
            </label>
            <div className="w-3/4">
              <p className="p-2 border h-11 rounded bg-gray-100">{guest.email}</p>
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Mobile Phone
            </label>
            <div className="w-3/4">
              <p className="p-2 border h-11 rounded bg-gray-100">
                {guest.mobilePhone || "N/A"}
              </p>
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Company
            </label>
            <div className="w-3/4">
              <p className="p-2 border h-11 rounded bg-gray-100">
                {guest.company || "N/A"}
              </p>
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Private Visitor?
            </label>
            <div className="w-3/4">
              <p className="p-2 border h-11 rounded bg-gray-100">
                {guest.isPrivate}
              </p>
            </div>
          </div>
          <div className="flex justify-center gap-4 mt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-gray-400 text-white rounded"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewGuestDetails;