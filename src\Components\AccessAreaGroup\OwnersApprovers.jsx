import React, { useState } from "react";
import GenericTable from "../GenericTable";
import { Tooltip } from "react-tooltip";
import { MdOutlineDeleteForever } from "react-icons/md";
import OwnersAdd from "./OwnersAdd"; 
import ApproversAdd from "./ApproversAdd";
import TruncatedCell from "../Tooltip/TruncatedCell";
import TruncatedRow from "../Tooltip/TrucantedRow";
import {AddApproversData, AddOwnersData} from "../../api/static"

const OwnersApprovers = () => {
  const [searchTerm1, setSearchTerm1] = useState("");
  const [ownersData, setOwnersData] = useState(AddOwnersData)
  const [approversData, setApproversData] = useState(AddApproversData)
  const [searchTerm2, setSearchTerm2] = useState("");
  // State for showing the add modals
  const [showAddModal, setShowAddModal] = useState(false);
  const [showApproversModal, setShowApproversModal] = useState(false);

  // Delete handlers for each table
  const handleDeleteOwner = (id) => {
    setOwnersData((prev) => prev.filter((item) => item.id !== id));
  };

  const handleDeleteApprover = (id) => {
    setApproversData((prev) => prev.filter((item) => item.id !== id));
  };

  const ownersColumns = [
    {
      name: "Name",
      selector: (row) => row.name,
      cell : row => <TruncatedRow text={row.name}/>,
      sortable: true,
    },
    {
      name: "EID",
      selector: (row) => row.eid,
      cell : row => <TruncatedRow text={row.eid}/>,
      sortable: true,
    },
    {
      name: "Type",
      selector: (row) => row.type,
      cell : row => <TruncatedRow text={row.type}/>,
      sortable: true,
    },
    {
      name:<TruncatedCell text="Organization"/>,
      selector: (row) => row.organization,
      cell : row => <TruncatedRow text={row.organization}/>,
      sortable: true,
    },
    {
      name: "Job Title",
      selector: (row) => row.jobTitle,
      cell : row => <TruncatedRow text={row.jobTitle}/>,
      sortable: true,
    },
    {
        name: "Status",
        selector: (row) => row.status,
        sortable: true,
        cell: (row) => (
          <span
            className={`w-20 py-1 flex justify-center items-center  rounded-full ${
              row.status === "Active"
                ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
                : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
            }`}
          >
            {row.status}
          </span>
        ),
      },
    {
      name: "Action",
      cell: (row) => (
        <MdOutlineDeleteForever
          size={32}
           className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5] text-red-600"
          onClick={() => handleDeleteOwner(row.id)}
        />
      ),
    },
  ];

  const approversColumns = [
    {
      name: "Name",
      selector: (row) => row.name,
      cell : row => <TruncatedRow text={row.name}/>,
      sortable: true,
    },
    {
      name: "EID",
      selector: (row) => row.eid,
      cell : row => <TruncatedRow text={row.eid}/>,
      sortable: true,
    },
    {
      name: <TruncatedCell text="Approver Level"/>,
      selector: (row) => row.approverLevel,
      cell : row => <TruncatedRow text={row.approverLevel}/>,
      sortable: true,
    },
    {
      name: "Type",
      selector: (row) => row.type,
      cell : row => <TruncatedRow text={row.type}/>,
      sortable: true,
    },
    {
      name:<TruncatedCell text="Organization"/>,
      selector: (row) => row.organization,
      cell : row => <TruncatedRow text={row.organization}/>,
      sortable: true,
    },
    {
      name: <TruncatedCell text="Job Title"/>,
      selector: (row) => row.jobTitle,
      cell : row => <TruncatedRow text={row.jobTitle}/>,
      sortable: true,
    },
    {
      name:<TruncatedCell text= "Start Date"/>,
      selector: (row) => row.startDate,
      cell : row => <TruncatedRow text={row.startDate}/>,
      sortable: true,
    },
    {
      name: <TruncatedCell text="End Date"/>,
      selector: (row) => row.endDate,
      cell : row => <TruncatedRow text={row.endDate}/>,
      sortable: true,
    },
    {
        name: "Status",
        selector: (row) => row.status,
        sortable: true,
        cell: (row) => (
          <span
            className={`w-20 py-1 flex justify-center items-center  rounded-full ${
              row.status === "Active"
                ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
                : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
            }`}
          >
            {row.status}
          </span>
        ),
      },
    {
      name: "Action",
      cell: (row) => (
        <MdOutlineDeleteForever
          size={32}
           className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5] text-red-600"
          onClick={() => handleDeleteApprover(row.id)}
        />
      ),
    },
  ];

  // When a new owner is added from the modal, add it to the top of the list.
  const handleAddOwner = (newOwner, action) => {
    const ownerToAdd = { ...newOwner, id: Date.now() };
    setOwnersData((prev) => [ownerToAdd, ...prev]);
    if (action === "add") {
      setShowAddModal(false);
    }
  };

  // When a new approver is added from the modal, add it to the top of the list.
  const handleAddApprover = (newApprover, action) => {
    const approverToAdd = { ...newApprover, id: Date.now() };
    setApproversData((prev) => [approverToAdd, ...prev]);
    if (action === "add") {
      setShowApproversModal(false);
    }
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="bg-white rounded-[10px]">
        <GenericTable
          title="Owner(s)"
          searchTerm={searchTerm1}
          onSearchChange={(e) => setSearchTerm1(e.target.value)}
          columns={ownersColumns}
          data={ownersData}
          showSearch={true}
          showAddButton={true}
          onAdd={() => setShowAddModal(true)}
        />
        <Tooltip id="owners-tooltip" place="top" effect="solid" />
      </div>
      <div className="bg-white rounded-[10px]">
        <GenericTable
          title="Approver(s)"
          searchTerm={searchTerm2}
          onSearchChange={(e) => setSearchTerm2(e.target.value)}
          columns={approversColumns}
          data={approversData}
          showSearch={true}
          showAddButton={true}
          onAdd={() => setShowApproversModal(true)}
        />
        <Tooltip id="approvers-tooltip" place="top" effect="solid" />
      </div>

      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
         <div className="bg-white shadow-lg p-1 rounded-lg w-[80%]">
         <div className="rounded-lg max-h-[90vh] ">
            <OwnersAdd
              onSubmit={handleAddOwner}
              onClose={() => setShowAddModal(false)}
              availableOwners={ownersData}
            />
          </div>
          </div>
        </div>
      )}

      {showApproversModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
         <div className="bg-white shadow-lg p-1 rounded-lg w-[80%]">
         <div className="rounded-lg max-h-[90vh] ">
            <ApproversAdd
              onSubmit={handleAddApprover}
              onClose={() => setShowApproversModal(false)}
              availableApprovers={approversData}
            />
          </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OwnersApprovers;
