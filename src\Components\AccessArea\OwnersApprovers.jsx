import React, { useState } from "react";
import GenericTable from "../GenericTable";
import { Tooltip } from "react-tooltip";
import { MdOutlineDeleteForever } from "react-icons/md";
import OwnersAdd from "./OwnersAdd"; 
import ApproversAdd from "./ApproversAdd"; 
import ApproversEdit from "./ApproversEdit"; 
import { FaEdit } from "react-icons/fa";
import TruncatedCell from "../Tooltip/TruncatedCell";
import Delete from "../../Images/Delete.svg";


const OwnersApprovers = () => {
  const [searchTerm1, setSearchTerm1] = useState("");
  const [ownersData, setOwnersData] = useState([
    {
      id: 1,
      name: "<PERSON>",
      eid: "E001",
      type: "Employee",
      organization: "Org1",
      jobTitle: "Developer",
      status: "Active",
    },
    {
      id: 2,
      name: "<PERSON>",
      eid: "E002",
      type: "Employee",
      organization: "Org1",
      jobTitle: "Designer",
      status: "Inactive",
    },
    {
      id: 3,
      name: "<PERSON> <PERSON>",
      eid: "E003",
      type: "Manager",
      organization: "Org2",
      jobTitle: "Project Manager",
      status: "Active",
    },
    {
      id: 4,
      name: "Bob <PERSON>",
      eid: "E004",
      type: "Employee",
      organization: "Org2",
      jobTitle: "QA Engineer",
      status: "Active",
    },
    {
      id: 5,
      name: "Charlie Green",
      eid: "E005",
      type: "Employee",
      organization: "Org3",
      jobTitle: "Support",
      status: "Inactive",
    },
  ]);

  const [searchTerm2, setSearchTerm2] = useState("");
  const [approversData, setApproversData] = useState([
    {
      id: 1,
      name: "Emma Wilson",
      eid: "A101",
      approverLevel: "Level 1",
      type: "Manager",
      organization: "Org1",
      jobTitle: "Supervisor",
      startDate: "2023-01-01",
      endDate: "2023-12-31",
      status: "Active",
    },
    {
      id: 2,
      name: "Liam Davis",
      eid: "A102",
      approverLevel: "Level 2",
      type: "Senior Manager",
      organization: "Org2",
      jobTitle: "Team Lead",
      startDate: "2023-02-01",
      endDate: "2023-11-30",
      status: "Inactive",
    },
    {
      id: 3,
      name: "Olivia Martinez",
      eid: "A103",
      approverLevel: "Level 1",
      type: "Manager",
      organization: "Org3",
      jobTitle: "Coordinator",
      startDate: "2023-03-01",
      endDate: "2023-10-31",
      status: "Active",
    },
    {
      id: 4,
      name: "Noah Anderson",
      eid: "A104",
      approverLevel: "Level 3",
      type: "Director",
      organization: "Org1",
      jobTitle: "Director",
      startDate: "2023-04-01",
      endDate: "2023-09-30",
      status: "Active",
    },
    {
      id: 5,
      name: "Sophia Thomas",
      eid: "A105",
      approverLevel: "Level 2",
      type: "Manager",
      organization: "Org2",
      jobTitle: "Project Lead",
      startDate: "2023-05-01",
      endDate: "2023-08-31",
      status: "Inactive",
    },
  ]);

  const [showAddModal, setShowAddModal] = useState(false);
  const [showApproversModal, setShowApproversModal] = useState(false);
  const [editApproverData, setEditApproverData] = useState(null);

  const handleDeleteOwner = (id) => {
    setOwnersData((prev) => prev.filter((item) => item.id !== id));
  };

  const handleDeleteApprover = (id) => {
    setApproversData((prev) => prev.filter((item) => item.id !== id));
  };

  const handleAddOwner = (newOwner, action) => {
    const ownerToAdd = { ...newOwner, id: Date.now() };
    setOwnersData((prev) => [ownerToAdd, ...prev]);
    if (action === "add") {
      setShowAddModal(false);
    }
  };

  const handleAddApprover = (newApprover, action) => {
    const approverToAdd = { ...newApprover, id: Date.now() };
    setApproversData((prev) => [approverToAdd, ...prev]);
    if (action === "add") {
      setShowApproversModal(false);
    }
  };

  const handleEditApprover = (row) => {
    setEditApproverData(row);
  };

  const ownersColumns = [
    {
      name: "Name",
      selector: (row) => row.name,
      sortable: true,
    },
    {
      name: "EID",
      selector: (row) => row.eid,
    },
    {
      name: "Type",
      selector: (row) => row.type,
    },
    {
      name: <TruncatedCell text="Organization" />,
      selector: (row) => row.organization,
    },
    {
      name: "Job Title",
      selector: (row) => row.jobTitle,
    },
    {
      name: "Status",
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`w-20 py-1 flex justify-center items-center rounded-full ${row.status === "Active"
              ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
              : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
            }`}
        >
          {row.status}
        </span>
      ),
    },
    {
      name: "Action",
      cell: (row) => (
        <img src={Delete} alt="Delete" 
         className="p-1.5 bg-[#F0EDF5] rounded-lg w-8 h-8 cursor-pointer" onClick={() => handleDeleteOwner(row.id)} />
      ),
    },
  ];

  const approversColumns = [
    {
      name: "Name",
      selector: (row) => row.name,
      sortable: true,
    },
    {
      name: "EID",
      selector: (row) => row.eid,
    },
    {
      name: <TruncatedCell text="Approver Level" />,
      selector: (row) => row.approverLevel,
    },
    {
      name: "Type",
      selector: (row) => row.type,
    },
    {
      name: <TruncatedCell text="Organization" />,
      selector: (row) => row.organization,
    },
    {
      name: "Job Title",
      selector: (row) => row.jobTitle,
    },
    {
      name: "Status",
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`w-20 py-1 flex justify-center items-center rounded-full ${row.status === "Active"
              ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
              : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
            }`}
        >
          {row.status}
        </span>
      ),
    },
    {
      name: "Action",
      cell: (row) => (
        <div className="flex space-x-2">
          <FaEdit

            size={32}
            className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5] text-green-500 items-center "
            onClick={() => handleEditApprover(row)}
          />
          
          <img src={Delete} alt="Delete" 
         className="p-1.5 bg-[#F0EDF5] rounded-lg w-8 h-8 cursor-pointer" onClick={() => handleDeleteApprover(row.id)} />

        </div>

      ),
    },

  ];

  return (
    <div className="flex flex-col gap-4">
      <div className="bg-white rounded-[10px]">
        <GenericTable
          title="Owner(s)"
          searchTerm={searchTerm1}
          onSearchChange={(e) => setSearchTerm1(e.target.value)}
          columns={ownersColumns}
          data={ownersData}
          showSearch={true}
          showAddButton={true}
          onAdd={() => setShowAddModal(true)}
        />
        <Tooltip id="owners-tooltip" place="top" effect="solid" />
      </div>
      <div className="bg-white rounded-[10px]">
        <GenericTable
          title="Approver(s)"
          searchTerm={searchTerm2}
          onSearchChange={(e) => setSearchTerm2(e.target.value)}
          columns={approversColumns}
          data={approversData}
          showSearch={true}
          showAddButton={true}
          onAdd={() => setShowApproversModal(true)}
        />
        <Tooltip id="approvers-tooltip" place="top" effect="solid" />
      </div>

      {/* Modal for adding a new owner */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
          <div className="bg-white shadow-lg p-1 rounded-lg w-[80%]">
            <div className="rounded-lg max-h-[90vh]">
              <OwnersAdd
                onSubmit={handleAddOwner}
                onClose={() => setShowAddModal(false)}
                availableOwners={ownersData}
              />
            </div>
          </div>
        </div>
      )}

      {/* Modal for adding a new approver */}
      {showApproversModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
          <div className="bg-white shadow-lg p-1 rounded-lg w-[80%]">
            <div className="rounded-lg max-h-[90vh]">
              <ApproversAdd
                onSubmit={handleAddApprover}
                onClose={() => setShowApproversModal(false)}
                availableApprovers={approversData}
              />
            </div>
          </div>
        </div>
      )}

      {/* Modal for editing an approver */}
      {editApproverData && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
          <div className="bg-white shadow-lg p-1 rounded-lg w-[80%]">
            <div className="rounded-lg max-h-[90vh]">
              <ApproversEdit
                onSubmit={(updatedData) => {
                  setApproversData((prev) =>
                    prev.map((item) =>
                      item.id === editApproverData.id
                        ? { ...item, ...updatedData }
                        : item
                    )
                  );
                  setEditApproverData(null);
                }}
                onClose={() => setEditApproverData(null)}
                availableApprovers={approversData}
                initialData={editApproverData}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OwnersApprovers;
