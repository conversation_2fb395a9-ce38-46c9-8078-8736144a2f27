import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import userImg from "../../Images/fromimg.svg";
import Identity from '../../Components/MyApp/MyProfile/Identity/Identity';
import Corporate from '../../Components/MyApp/MyProfile/Corporate/Corporate';
import Cards from '../../Components/MyApp/MyProfile/Cards/Cards';
import Access from '../../Components/MyApp/MyProfile/Access/Access';
import Delegates from '../../Components/MyApp/MyProfile/Delegates/Delegates';
import Vehicles from '../../Components/MyApp/MyProfile/Vehicles/Vehicles';
import DetailsCard from '../../Components/Global/DetailsCard';
import EditPhotoModal from '../../Components/Global/ImageAndCamera/EditPhotoModal'; 
import Requests from '../../Components/MyApp/MyProfile/Requests/Requests';

const MyProfile = () => {
  const location = useLocation(); // Get location object
  const queryParams = new URLSearchParams(location.search); // Parse query parameters
  const initialTab = queryParams.get('tab') || 'Identity'; // Get tab from query or default to 'Identity'
  const { t } = useTranslation();

  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [isEditingOtherInfo, setIsEditingOtherInfo] = useState(false);
  const [selectedTab, setSelectedTab] = useState(initialTab); // Initialize with query parameter
  const [profileImage, setProfileImage] = useState(null); // Added state for profile image
  const [isModalOpen, setIsModalOpen] = useState(false); // Added state for modal

  const [profileDetails, setProfileDetails] = useState({
    firstName: "Adam",
    middleName: "",
    lastName: "LETHELAN",
    preferredName: "ADAM LETHELAN",
    email: "<EMAIL>",
    workPhone: "123-123456",
    mobilePhone: "1234567890",
  });

  const [otherInfo, setOtherInfo] = useState({
    eid: "1234",
  });

  const handleProfileChange = (e) => {
    const { name, value } = e.target;
    setProfileDetails((prev) => ({ ...prev, [name]: value }));
  };

  const handleOtherInfoChange = (e) => {
    const { name, value } = e.target;
    setOtherInfo((prev) => ({ ...prev, [name]: value }));
  };

  const handleImageCaptured = (imageSrc) => {
    setProfileImage(imageSrc); // Update profile image
    setIsModalOpen(false); // Close modal
  };

  const handleCloseModal = () => {
    setIsModalOpen(false); // Close modal
  };

  React.useEffect(() => {
    setSelectedTab(initialTab); // Update selectedTab when query parameter changes
    const openPhotoModal = queryParams.get('openPhotoModal') === 'true'; // Check if photo modal should open
    if (openPhotoModal) {
      setIsModalOpen(true); // Open photo modal
    }
  }, [initialTab, queryParams]);

  return (
    <div className="bg-gray-100 min-h-screen p-14 pl-28 pt-16">
      {/* Profile Section */}
      <DetailsCard
        OpenPhotoModal={() => setIsModalOpen(true)} // Open modal on click
        profileImage={profileImage} // Pass profile image
        defaultImage={userImg}
        name="ADAM L'THELAN"
        additionalFields={[
          { label: t('my_profile.type'), value: t('my_profile.employee') },
          { label: t('my_profile.eid'), value: "1222535" },
          { label: t('my_profile.department'), value: "ABC" },
          { label: t('my_profile.manager'), value: "Nema" },
          { label: t('my_profile.status'), value: t('my_profile.unprinted_badges') },
        ]}
      />

      <div className="flex gap-8">
        <div className="w-1/12 mt-2">
          {['Identity', 'Corporate', 'Cards', 'Access', 'Delegates', 'Vehicles', 'Requests'].map((tab) => (
            <button
              key={tab}
              className={`block w-full text-left p-2 mb-2 ${
                selectedTab === tab
                  ? 'text-[#4F2683] text-[14px] font-poppins border-l-2 border-[#4F2683]'
                  : 'text-[14px] text-gray-700 font-poppins'
              }`}
              onClick={() => setSelectedTab(tab)}
            >
              {t(`my_profile.tabs.${tab.toLowerCase()}`)}
            </button>
          ))}
        </div>

        <div className="w-11/12 ml-3">
          {selectedTab === 'Identity' && <Identity />}
          {selectedTab === 'Corporate' && <Corporate />}
          {selectedTab === 'Cards' && <Cards />}
          {selectedTab === 'Access' && <Access />}
          {selectedTab === 'Delegates' && <Delegates />}
          {selectedTab === 'Vehicles' && <Vehicles />}
          {selectedTab === 'Requests' && <Requests />}
        </div>
      </div>

      {isModalOpen && (
        <EditPhotoModal
          onClose={handleCloseModal} // Close modal on "Close"
          onSave={(imageSrc) => {
            handleImageCaptured(imageSrc); // Save image and close modal
          }}
        />
      )}
    </div>
  );
};

export default MyProfile;
