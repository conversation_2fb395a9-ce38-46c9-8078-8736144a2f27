import React, { useState, useEffect } from "react";
import GenericTable from "../GenericTable";
import Swal from "sweetalert2";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import TruncatedRow from "../Tooltip/TrucantedRow";
import Delete from "../../Images/Delete.svg";
import { FiEdit2 } from "react-icons/fi";
import { MdQrCode2 } from "react-icons/md";
import AddDeviceModal from "./AddDeviceModal";
import EditDeviceModal from "./EditDeviceModal";
import DeviceViewModal from "./DeviceViewModal";
import DeviceQrModal from "./DeviceQrModal";

const Device = () => {
  // State
  const [data, setData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("device");
  const [sortOrder, setSortOrder] = useState("ASC");
  const [selectedRow, setSelectedRow] = useState(null);

  // Modal flags
  const [isAddModalOpen, setAddModalOpen] = useState(false);
  const [isEditModalOpen, setEditModalOpen] = useState(false);
  const [isQRModalOpen, setQRModalOpen] = useState(false);
  const [isViewModalOpen, setViewModalOpen] = useState(false);

  // New entry form
  const [newEntry, setNewEntry] = useState({
    device: "",
    deviceGroup: "",
    building: "",
    floor: "",
    room: "",
  });

  // Fetch (dummy) devices on mount or when sort changes
  useEffect(() => {
    const fetchDevices = async () => {
      try {
        // replace with real API call
        const response = [
          { id: 1, device: "device 1", deviceGroup: "group 1", building: "Commercial", floor: "12", room: "12" },
          { id: 2, device: "device 2", deviceGroup: "group 2", building: "Data center", floor: "34", room: "34" },
          { id: 3, device: "device 3", deviceGroup: "group 3", building: "Commercial", floor: "654", room: "654" },
          { id: 4, device: "device 4", deviceGroup: "group 4", building: "Educational", floor: "324", room: "324" },
          { id: 5, device: "device 5", deviceGroup: "group 5", building: "Educational", floor: "23", room: "25" },
          { id: 6, device: "device 6", deviceGroup: "group 6", building: "Commercial", floor: "25", room: "25" },
          { id: 7, device: "device 7", deviceGroup: "group 7", building: "Data center", floor: "23", room: "23" },
        ];
        setData(response);
        setFilteredData(response);
      } catch {
        toast.error("Failed to fetch devices.");
      }
    };
    fetchDevices();
  }, [sortBy, sortOrder]);

  // Search/filter
  useEffect(() => {
    const term = searchTerm.trim().toLowerCase();
    setFilteredData(
      term
        ? data.filter(d =>
            [d.device, d.deviceGroup, d.building, d.floor, d.room]
              .some(field => field.toLowerCase().includes(term))
          )
        : data
    );
  }, [data, searchTerm]);

  // Handlers
  const handleSearch = e => setSearchTerm(e.target.value);
  const handleSort = (column, dir) => {
    setSortBy(column.id);
    setSortOrder(dir.toUpperCase());
  };
  const handleView = row => {
    setSelectedRow(row);
    setViewModalOpen(true);
  };
  const handleEdit = row => {
    setSelectedRow(row);
    setEditModalOpen(true);
  };
  const handleDelete = id => {
    Swal.fire({
      title: "Are you sure?",
      text: "Do you really want to delete this device?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, delete it!",
    }).then(result => {
      if (result.isConfirmed) {
        setData(prev => prev.filter(item => item.id !== id));
        toast.success("Device deleted successfully!");
      }
    });
  };
  const handleQR = row => {
    setSelectedRow(row);
    setQRModalOpen(true);
  };
  const handleAddSave = () => {
    const device = { id: Date.now(), ...newEntry };
    setData(prev => [device, ...prev]);
    setNewEntry({ device: "", deviceGroup: "", building: "", floor: "", room: "" });
    setAddModalOpen(false);
    toast.success("Device added successfully!");
  };
  const handleEditSave = () => {
    setData(prev =>
      prev.map(item => (item.id === selectedRow.id ? selectedRow : item))
    );
    setEditModalOpen(false);
    toast.success("Device updated successfully!");
  };

  // Table cols
  const columns = [
    {
      id: "device",
      name: "Device",
      selector: row => row.device,
      sortable: true,
      cell: row => (
        <span
          className="text-[#4F2683] underline cursor-pointer"
          onClick={() => handleView(row)}
        >
          <TruncatedRow text={row.device} />
        </span>
      ),
    },
    {
      id: "deviceGroup",
      name: "Device Group",
      selector: row => row.deviceGroup,
      sortable: true,
      cell: row => <TruncatedRow text={row.deviceGroup} />,
    },
    {
      id: "building",
      name: "Building",
      selector: row => row.building,
      sortable: true,
      cell: row => <TruncatedRow text={row.building} />,
    },
    {
      id: "floor",
      name: "Floor",
      selector: row => row.floor,
      sortable: true,
      cell: row => <TruncatedRow text={row.floor} />,
    },
    {
      id: "room",
      name: "Room",
      selector: row => row.room,
      sortable: true,
      cell: row => <TruncatedRow text={row.room} />,
    },
    {
      name: "Action",
      button: true,
      ignoreRowClick: true,
      center: true,
      allowOverflow: true,
      cell: row => (
        <div className="flex items-center gap-2">
          <img
            src={Delete}
            alt="Delete"
            className="p-1.5 bg-[#F0EDF5] rounded-lg w-8 h-8 cursor-pointer"
            onClick={() => handleDelete(row.id)}
          />
          <FiEdit2
            className="p-1.5 bg-[#F0EDF5] rounded-lg w-8 h-8 cursor-pointer text-[#4F2683]"
            onClick={() => handleEdit(row)}
          />
          <MdQrCode2
            className="p-1.5 bg-[#F0EDF5] rounded-lg w-8 h-8 cursor-pointer text-[#4F2683]"
            onClick={() => handleQR(row)}
          />
        </div>
      ),
    },
  ];

  return (
    <div className="relative">
      <div className="bg-white rounded-[10px]">
        <GenericTable
          title="Devices"
          searchTerm={searchTerm}
          onSearchChange={handleSearch}
          onAdd={() => setAddModalOpen(true)}
          columns={columns}
          data={filteredData}
          onSort={handleSort}
          sortServer
          showSearch
          showAddButton
        />
      </div>

      <ToastContainer />

      <AddDeviceModal
        open={isAddModalOpen}
        onClose={() => setAddModalOpen(false)}
        onSave={handleAddSave}
        newEntry={newEntry}
        setNewEntry={setNewEntry}
      />

      <EditDeviceModal
        open={isEditModalOpen}
        onClose={() => setEditModalOpen(false)}
        onSave={handleEditSave}
        selectedRow={selectedRow}
        setSelectedRow={setSelectedRow}
      />

      <DeviceQrModal
        open={isQRModalOpen}
        device={selectedRow}
        onClose={() => setQRModalOpen(false)}
      />

      <DeviceViewModal
        device={isViewModalOpen ? selectedRow : null}
        onClose={() => setViewModalOpen(false)}
      />
    </div>
  );
};

export default Device;
