import React, { useState } from 'react';
import Facility from '../../Components/Facility/Facilitery';
import Building from '../../Components/Facility/Building/Building';
import Floor from '../../Components/Facility/Floor/Floor';
import Room from '../../Components/Facility/Room/Room';
import AccessAreas from '../../Components/Facility/AccessArea/AccessAreas';
import DetailsCard from '../../Components/Global/DetailsCard';
import EditPhotoModal from '../../Components/Global/ImageAndCamera/EditPhotoModal';
import userImg from '../../Images/Building.svg'
import { useNavigate } from 'react-router-dom';
import { IoIosArrowBack } from "react-icons/io";
import HistoryTable from '../../Components/Observation/HistoryTable';
const WatchListDetails = () => {
  const [selectedTab, setSelectedTab] = useState('Facility');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [profileImage, setProfileImage] = useState(null);
  const navigate = useNavigate();

  const [isHistoryPanelOpen, setIsHistoryPanelOpen] = useState(false);

  const handleHistoryOpen = () => setIsHistoryPanelOpen(true);

  // Function to handle image capture
  const handleImageCaptured = (imageSrc) => {
    setProfileImage(imageSrc); 
    setIsModalOpen(false);
  };


  return (
    <div className="bg-gray-100 min-h-screen p-8 pl-20 pt-20">
      {/* Profile Section */}
      <div className="flex items-center text-[#4F2683]">
        <div className='flex items-center gap-1 cursor-pointer' onClick={() => navigate('/facility')}>
          <IoIosArrowBack className="text-[#4F2683] font-normal text-[24px]" />
          <h2 className="font-normal text-[24px]">Facility</h2>
        </div>
      </div>
      <DetailsCard
      OpenPhotoModal={() => setIsModalOpen(true)}
      handleHistoryOpen={handleHistoryOpen}
      profileImage={profileImage}
      defaultImage={userImg}
      name="India Elnath"
      showHistoryButton={true}
      additionalFields={[
        { label: "Address", value: "123 Main St" },
        { label: "Status", value: "Active" },
        { label: "Type", value: "Commercial" }
    ]}
      />

      {/* Sidebar and Main Content */}
      <div className="flex">
        <div className="w-[12%] mt-4">
          {['Facility', 'Building', 'Floor', 'Room', 'Access Areas'].map((tab) => (
            <button
              key={tab}
              className={`block w-full text-left p-2 mb-2 ${selectedTab === tab ? 'text-[#4F2683] border-l-2 border-[#4F2683]' : 'text-gray-700'}`}
              onClick={() => setSelectedTab(tab)}
            >
              {tab}
            </button>
          ))}
        </div>

        {/* Main Content */}
        <div className="w-[88%] ">
          {selectedTab === 'Facility' && <Facility />}
          {selectedTab === 'Building' && <Building />}
          {selectedTab === 'Floor' && <Floor />}
          {selectedTab === 'Room' && <Room />}
          {selectedTab === 'Access Areas' && <AccessAreas />}
        </div>
      </div>
      <HistoryTable
        isOpen={isHistoryPanelOpen}
        onClose={() => setIsHistoryPanelOpen(false)} />
      {isModalOpen && (
        <EditPhotoModal
          // title={modalTitle}
          onClose={() => setIsModalOpen(false)}
          onSave={handleImageCaptured}
        />
  
      )}
    </div>
  );
};

export default WatchListDetails;
