import React from "react";
import { useSelector } from "react-redux";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import Input from "../../Components/Global/Input/Input";
import DateTimeInput from "./DateTimeInput";
import Button from "../../Components/Global/Button";
import SearchableDropdown from "../Global/SearchableDropdownIdentity";
import CustomDropdown from "../Global/CustomDropdown";


const TemporaryCardAdd = ({ onClose, onAdd, initialValues: editValues }) => {
  // Get the facility from Redux
  const selectedFacility = useSelector((state) => state.facility.selected);

  // Default initial values for a new temporary card.
  const defaultInitialValues = {
    searchIdentity: { name: "", eid: "", company: "", manager: "" },
    activationDateTime: new Date(),
    deactivationDateTime:"",
    cardType: "Temporary Card",
    cardNumber: "",
    facilityBuilding: selectedFacility,
    justification: "",
  };

  // Use edit values if provided, otherwise fallback to default.
  const initialValues = editValues || defaultInitialValues;
  const validationSchema = Yup.object().shape({
    searchIdentity: Yup.object().shape({
      name: Yup.string().required("Search Identity is required"),
      eid: Yup.string().required("Identity EID is required"),
    }),
    activationDateTime: Yup.date().required("Activation Date/Time is required"),
    deactivationDateTime: Yup.date()
      .required("Deactivation Date/Time is required")
      .min(
        Yup.ref("activationDateTime"),
        "Deactivation must be after activation"
      ),
    cardType: Yup.string().required("Card Type is required"),
    cardNumber: Yup.string().required("Card Number is required"),
  });
  

  const handleSubmit = (values) => {
    const activationStr = values.activationDateTime.toLocaleString("en-US", {
      day: "2-digit",
      month: "short",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
    const deactivationStr = values.deactivationDateTime.toLocaleString("en-US", {
      day: "2-digit",
      month: "short",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });

    const newCard = {
      name: values.searchIdentity.name,
      EID: values.searchIdentity.eid,
      type: values.cardType,
      hiringCompany: values.searchIdentity.company,
      facility: values.facilityBuilding,
      manager: values.searchIdentity.manager,
      tempCardNo: values.cardNumber,
      justification: values.justification, 
      activationDateTime: activationStr,
      deactivationDateTime: deactivationStr,
      status: "Not Returned",
    };

    onAdd(newCard);
  };
  const cardTypeOptions =[
    {label:"Temporary Card" , value:"Temporary Card"},
    {label:"Permanent Card" , value:"Permanent Card"},
  ]
  const [show, setShow] = React.useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50 transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`} style={{ willChange: "transform" }}>
      <div className="bg-white w-full h-auto max-w-5xl p-4 rounded-lg shadow-lg overflow-y-auto h-full max-h-full">
        <div className="flex justify-between items-center px-2 pt-2">
          <h2 className="text-[30px] text-[#4F2683] font-normal">
            {editValues ? "Edit Temporary Card" : "Add Temporary Card"}
          </h2>
          <button
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
            className="flex items-center justify-center bg-[#4F2683] text-white text-2xl rounded-full h-8 w-8 hover:bg-[#6A3BAA]"
          >
            &times;
          </button>
        </div>
        <hr className="mb-4 mt-2" />
        <Formik
          initialValues={initialValues}
          enableReinitialize
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ values, errors, touched, setFieldValue }) => (
            <Form className="space-y-4">
              <div className="space-y-4">
                {/* Search Identity Field */}
                <div className="flex mb-2 items-center">
                  <label className="mr-2 w-1/3">Search Identity *</label>
                  <div className="w-2/3">
                    <SearchableDropdown
                      value={values.searchIdentity?.name || ""}
                      onSelect={(selected) =>
                        setFieldValue("searchIdentity", selected)
                      }
                    />
                    {touched.searchIdentity?.name && errors.searchIdentity?.name && (
                      <div className="text-red-500 text-sm">
                        {errors.searchIdentity.name}
                      </div>
                    )}
                  </div>
                </div>
                {/* Activation Date/Time Field */}
                <div className="flex mb-2 items-center">
                  <label className="mr-2 w-1/3">Activation Date/Time *</label>
                  <div className="w-2/3">
                    <DateTimeInput
                      name="activationDateTime"
                      value={values.activationDateTime}
                      onChange={(date) =>
                        setFieldValue("activationDateTime", date)
                      }
                      placeholder="Select activation date & time"
                    />
                    {touched.activationDateTime && errors.activationDateTime && (
                      <div className="text-red-500 text-sm">
                        {errors.activationDateTime}
                      </div>
                    )}
                  </div>
                </div>
                {/* Deactivation Date/Time Field */}
                <div className="flex mb-2 items-center">
                  <label className="mr-2 w-1/3">Deactivation Date/Time *</label>
                  <div className="w-2/3">
                    <DateTimeInput
                      name="deactivationDateTime"
                      value={values.deactivationDateTime}
                      onChange={(date) =>
                        setFieldValue("deactivationDateTime", date)
                      }
                      placeholder="Select deactivation date & time"
                      minDate={values.activationDateTime}
                    />
                    {touched.deactivationDateTime && errors.deactivationDateTime && (
                      <div className="text-red-500 text-sm">
                        {errors.deactivationDateTime}
                      </div>
                    )}
                  </div>
                </div>
                {/* Card Type Field */}
                <div className="flex mb-2 items-center">
                  <label className="mr-2 w-1/3">Card type *</label>
                  <div className="w-2/3">
                    <CustomDropdown
                      options={cardTypeOptions}
                      placeholder="Card Type"
                      value={values.cardType}
                      className="h-10"
                      onSelect={(selectedValue) =>
                        setFieldValue("cardType", selectedValue)
                      }
                      error={touched.cardType && errors.cardType ? { message: errors.cardType } : null}
                    />
                  </div>
                </div>
                {/* Card Number Field */}
                <div className="flex mb-2 items-center">
                  <label className="mr-2 w-1/3">Card Number *</label>
                  <div className="w-2/3">
                    <Input
                      type="number"
                      name="cardNumber"
                      placeholder="Card Number"
                      value={values.cardNumber}
                      onChange={(e) => setFieldValue("cardNumber", e.target.value)}
                    />
                    {touched.cardNumber && errors.cardNumber && (
                      <div className="text-red-500 text-sm">{errors.cardNumber}</div>
                    )}
                  </div>
                </div>
                {/* Facility/Building Field */}
                <div className="flex mb-2 items-center">
                  <label className="mr-2 w-1/3">Facility/Building *</label>
                  <div className="w-2/3">
                    <Input
                      name="facilityBuilding"
                      placeholder="Facility/Building"
                      value={values.facilityBuilding}
                      readOnly={true}
                      onChange={(e) =>
                        setFieldValue("facilityBuilding", e.target.value)
                      }
                    />
                    {touched.facilityBuilding && errors.facilityBuilding && (
                      <div className="text-red-500 text-sm">
                        {errors.facilityBuilding}
                      </div>
                    )}
                  </div>
                </div>
                {/* Justification Field */}
                <div className="flex mb-2">
                  <label className="mr-2 w-1/3">Justification</label>
                  <div className="w-2/3">
                    <Input
                      name="justification"
                      type="bubbles"
                      placeholder="Justification"
                      value={values.justification}
                      height="94px"
                      bubbles={true}
                      bubbleOptions={[
                        "Lost Permanent Card",
                        "Forgot Permanent Card",
                      ]}
                      onChange={(e) =>
                        setFieldValue("justification", e.target.value)
                      }
                    />
                    {touched.justification && errors.justification && (
                      <div className="text-red-500 text-sm">
                        {errors.justification}
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className="flex justify-center gap-4 mt-6">
                <Button type="cancel" label="Cancel" onClick={onClose} />
                <Button type="primary" label={editValues ? "Update" : "Add"} buttonType="submit" />
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default TemporaryCardAdd;
