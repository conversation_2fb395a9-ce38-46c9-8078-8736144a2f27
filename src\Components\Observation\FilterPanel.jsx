import React, { useState, useEffect } from "react";
import { IoClose } from "react-icons/io5";
import Input from "../Global/Input/Input";

const FilterPanel = ({ isOpen, onClose }) => {
    const [mainCategories, setMainCategories] = useState([
        "Lorem Ipsum 1", "Lorem Is 2", "Lorem Ipsum Is 3","Lorem Ipsum Is 4"
    ]);
    
    const [filters, setFilters] = useState({
        "Lorem Ipsum 1": {
            "Criminals Filter Data": ["Filter A1", "Filter A2", "Filter A3"],
            "Friends Filter Data": ["Friend A1", "Friend A2"]
        },
        "Lorem Is 2": {
            "Criminals Filter Data": ["Filter B1", "Filter B2"],
            "Friends Filter Data": ["Friend B1", "Friend B2", "Friend B3"]
        },
        "Lorem Ipsum Is 3": {
            "Criminals Filter Data": ["Filter C1", "Filter C2", "Filter C3"],
            "Friends Filter Data": ["Friend C1","Friend C2"]
        },
        "Lorem Ipsum Is 4": {
            "Criminals Filter Data": ["Filter D1", "Filter D2", "Filter D3"],
            "Friends Filter Data": ["Friend D1","Friend D2"]
        },
    });

    const [selectedMainCategory, setSelectedMainCategory] = useState(mainCategories[0]);
    const [selectedFilters, setSelectedFilters] = useState({});
    const [searchQuery, setSearchQuery] = useState("");
    const [show, setShow] = useState(false);

    useEffect(() => {
        setSelectedMainCategory(mainCategories[0]);
    }, [mainCategories]);

    useEffect(() => {
        if (isOpen) {
            setTimeout(() => setShow(true), 10);
        } else {
            setShow(false);
        }
    }, [isOpen]);

    const handleFilterToggle = (category, filter) => {
        setSelectedFilters((prev) => {
            const currentFilters = prev[category] || [];
            return {
                ...prev,
                [category]: currentFilters.includes(filter)
                    ? currentFilters.filter((f) => f !== filter)
                    : [...currentFilters, filter],
            };
        });
    };

    const handleApply = () => {
        console.log("Applied Filters:", selectedFilters);
        alert("Filters applied successfully!");
        onClose();
    };

    const filteredFilters = selectedMainCategory ? Object.keys(filters[selectedMainCategory]).reduce((acc, category) => {
        acc[category] = filters[selectedMainCategory][category].filter((filter) =>
            filter.toLowerCase().includes(searchQuery.toLowerCase())
        );
        return acc;
    }, {}) : {};

    return (
        <div className={`fixed top-0 left-0 h-full w-full bg-gray-900 bg-opacity-50 z-40 no-text-select ${isOpen ? "block" : "hidden"}`}>
            <div className={`fixed top-0 right-0 h-full w-[70%] rounded-l-md bg-white shadow-lg transform transition-transform duration-700 ease-in-out z-10 ${
                show ? "translate-x-0" : "translate-x-full"
            }`}
            style={{ willChange: "transform" }}>
                <div className="flex justify-between items-center p-4">
                    <h2 className="text-[30px] font-normal text-[#4F2386]">Filters</h2>
                    <div className="bg-[#4F2683] rounded-full cursor-pointer p-1" onClick={() => {
                        setShow(false);
                        setTimeout(onClose, 700);
                    }}>
                        <IoClose className=" text-white font-bold" />
                    </div>
                </div>
                <div className="grid grid-cols-3 gap-4 h-[90%] p-4 border-t border-b ">
                    {/* First Column: Main Categories */}
                    <div className="col-span-1 border-r pr-4">
                        <h3 className="text-[20px] font-normal mb-2">Select Category</h3>
                        <div className="p-4 rounded-lg">
                            {mainCategories.map((category) => (
                                <p 
                                    key={category} 
                                    className={`cursor-pointer mb-1 p-2 rounded-lg ${selectedMainCategory === category ? "text-[#4F2683] bg-[#EEE9F2]" : "text-[#7C7C7C] hover:bg-gray-200"}`}
                                    onClick={() => setSelectedMainCategory(category)}
                                >
                                    {category}
                                </p>
                            ))}
                        </div>
                    </div>

                    {/* Second Column: Filter Options */}
                    <div className="col-span-1 border-r pr-4">
                        <Input type="text" placeholder="Search" value={searchQuery} onChange={(e) => setSearchQuery(e.target.value)} />
                        {selectedMainCategory && (
                            <div className="p-4 rounded-lg">
                                {Object.keys(filteredFilters).map((category) => (
                                    <div key={category}>
                                        <h3 className="text-[20px] font-normal mb-2">{category}</h3>
                                        {filteredFilters[category].map((filter) => (
                                            <div key={filter} className="mb-2 flex items-center">
                                                <input
                                                    type="checkbox"
                                                    className="mr-2"
                                                    checked={selectedFilters[category]?.includes(filter) || false}
                                                    onChange={() => handleFilterToggle(category, filter)}
                                                />
                                                <label className="text-gray-700">{filter}</label>
                                            </div>
                                        ))}
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>

                    {/* Third Column: Selected Filters */}
                    <div className="col-span-1 no-text-select relative">
                        <div className="p-2 pb-4">
                            <h3 className="text-[20px] font-normal mb-6">Selected Filters</h3>
                            {Object.keys(selectedFilters).map((category) => (
                                <div key={category}>
                                    <h4 className="text-[20px] mb-2 font-normal">{category}</h4>
                                    <div>
                                        {selectedFilters[category].map((filter) => (
                                            <div key={filter} className="flex justify-between cursor-pointer mb-2">
                                                <span className="flex items-center bg-[#EEE9F2]  text-[#4F2683] px-2 py-1 rounded-full text-sm">
                                                    {filter}
                                                    <button className="ml-2 text-white rounded-full bg-[#4F2683] px-1.5" onClick={() => handleFilterToggle(category, filter)}>&times;</button>
                                                </span>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            ))}
                        </div>
                        <button className="absolute bottom-0 left-0 w-full bg-[#4F2683] text-white px-6 py-2 rounded-lg hover:bg-[#6A3BAA]" onClick={handleApply}>
                            Apply
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default FilterPanel;
