import React, { useState, useEffect } from "react";
import Button from "../Global/Button";
import CustomDropdown from "../Global/CustomDropdown";
import { useSelector } from "react-redux";

// Hooks for fetching dropdown data
import { useBuildingData } from "../../hooks/useBuildingData";
import { useFloorData } from "../../hooks/useFloorData";
import { useRoomData } from "../../hooks/useRoomData";

const AddDeviceModal = ({ open, onClose, onSave, newEntry, setNewEntry }) => {
  const [show, setShow] = useState(false);

  // Get current facility from Redux store
  const selectedFacilityId = useSelector(state => state.facility.selectedFacilityId);
  const selectedFacilityName = useSelector(state => state.facility.selectedFacilityName);

  // Building/Floor/Room selection state
  const [selectedBuilding, setSelectedBuilding] = useState('');
  const [selectedFloor, setSelectedFloor] = useState('');

  // Fetch dropdown options using hooks
  const buildingOptions = useBuildingData(selectedFacilityId);
  const floorOptions = useFloorData(selectedBuilding);
  const roomOptions = useRoomData(selectedFloor);

  console.log("AddDeviceModal: selectedFacilityId from Redux:", selectedFacilityId);
  console.log("AddDeviceModal: buildingOptions:", buildingOptions);



  useEffect(() => {
    if (open) {
      setTimeout(() => setShow(true), 10);
    } else {
      setShow(false);
    }
  }, [open]);

  if (!open) return null;

  const handleClose = () => {
    setShow(false);
    setTimeout(onClose, 700);
  };

  const handleFormSubmit = (e) => {
    e.preventDefault();
    // Update newEntry with facility information before saving
    const updatedEntry = {
      ...newEntry,
      facility: selectedFacilityName,
      facilityId: selectedFacilityId
    };
    setNewEntry(updatedEntry);
    onSave();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-white p-6 rounded shadow-lg w-full max-w-3xl h-full overflow-y-auto transform transition-transform duration-700 ease-in-out ${
          show ? "translate-x-0" : "translate-x-full"
        }`}
        style={{ willChange: "transform" }}
      >
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-[30px] font-normal text-[#4F2683]">Add Device</h3>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={handleClose}
          >
            &times;
          </button>
        </div>
        <hr className="mb-4" />
        <form className="bg-white p-2 rounded-lg" onSubmit={handleFormSubmit}>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Device*
            </label>
            <div className="w-3/4">
              <input
                className="border p-2 w-full rounded"
                placeholder="Device"
                value={newEntry.device}
                onChange={e => setNewEntry({ ...newEntry, device: e.target.value })}
                required
              />
            </div>
          </div>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Device Group*
            </label>
            <div className="w-3/4">
              <input
                className="border p-2 w-full rounded"
                placeholder="Device Group"
                value={newEntry.deviceGroup}
                onChange={e => setNewEntry({ ...newEntry, deviceGroup: e.target.value })}
                required
              />
            </div>
          </div>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Facility*
            </label>
            <div className="w-3/4">
              <input
                className="border p-2 w-full rounded bg-gray-100"
                value={selectedFacilityName || 'No facility selected'}
                readOnly
              />
            </div>
          </div>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Building*
            </label>
            <div className="w-3/4">
              <CustomDropdown
                options={buildingOptions}
                value={selectedBuilding}
                onSelect={(value) => {
                  setSelectedBuilding(value);
                  // Reset dependent fields when building changes
                  setSelectedFloor('');
                  setNewEntry({ ...newEntry, building: value, floor: '', room: '' });
                }}
                placeholder="Select Building"
                className="w-full h-10"
                disabled={!selectedFacilityId}
              />
            </div>
          </div>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Floor*
            </label>
            <div className="w-3/4">
              <CustomDropdown
                options={floorOptions}
                value={selectedFloor}
                onSelect={(value) => {
                  setSelectedFloor(value);
                  // Reset room when floor changes
                  setNewEntry({ ...newEntry, floor: value, room: '' });
                }}
                placeholder="Select Floor"
                className="w-full h-10"
                disabled={!selectedBuilding}
              />
            </div>
          </div>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Room*
            </label>
            <div className="w-3/4">
              <CustomDropdown
                options={roomOptions}
                value={newEntry.room}
                onSelect={(value) => {
                  setNewEntry({ ...newEntry, room: value });
                }}
                placeholder="Select Room"
                className="w-full h-10"
                disabled={!selectedFloor}
              />
            </div>
          </div>
          <div className="flex justify-center gap-4">
            <Button
              type="cancel"
              onClick={handleClose}
              label="Cancel"
            />
            <Button
              type="primary"
              label="Save"
            />
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddDeviceModal;
