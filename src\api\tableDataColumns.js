import In from "../Images/in.svg";
import <PERSON><PERSON> from "../Images/camera.svg";
import Chat from "../Images/chat.svg";
import Print from "../Images/print.svg";
import Out from "../Images/out.svg";
import React from "react";
import TruncatedCell from "../Components/Tooltip/TruncatedCell";

// import Cemara from "../../Images/camera.svg";
// import Chat from "../../Images/chat.svg";
// import Print from "../../Images/print.svg";
// import In from "../../Images/in.svg";
// import Out from "../../Images/out.svg";

/**
 * Returns the column configuration for the Visitor table.
 *
 * @param {Object} params
 * @param {Function} params.openModal - Handler to open the modal (used in Guest Action column)
 * @param {Function} params.handlePrintClick - Handler for print action
 * @param {string} params.profileImage - Fallback image URL if none is provided in the data
 */
export const getVisitorColumns = ({ openModal, handlePrintClick, profileImage }) => [
  {
    name: "Name",
    cell: (row) => (
      <div className="flex items-center">
        <img
          src={row.image || profileImage}
          alt={row.visitorName}
          className="w-8 h-8 rounded-full mr-2"
        />
        <span>{row.visitorName}</span>
      </div>
    ),
    sortable: true,
  },
  {
    name: "Host",
    selector: (row) => row.hostName || "N/A",
  },
  {
    name: "Facility",
    selector: (row) => row.facility || "N/A",
  },
  {
    name:<TruncatedCell text= "Start Date & Time"/>,
    selector: (row) => row.startDate || "N/A",
  },
  {
    name:<TruncatedCell text= "End Date & Time"/>,
    selector: (row) => row.endDate || "N/A",
  },
  {
    name: "Guest Action",
    cell: (row) => (
      <div className="flex justify-center items-center space-x-2">
        <img
          src={Cemara}
          alt="Camera"
          className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
          onClick={() => openModal("Update Profile Image", row.id)}
        />
        <img
          src={Chat}
          alt="Chat"
          className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
          onClick={() => console.log("Chat clicked for:", row.visitorName)}
        />
        <img
          src={Print}
          alt="Print"
          className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
          onClick={() => handlePrintClick(row)}
        />
        <img
          src={In}
          alt="In"
          className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
          onClick={() => console.log("In clicked for:", row.visitorName)}
        />
        <img
          src={Out}
          alt="Out"
          className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
          onClick={() => console.log("Out clicked for:", row.visitorName)}
        />
      </div>
    ),
    center: true,
  },
];

export const TodayVisitorsColumns  = ({ openModal, handlePrintClick,profileImage }) => [
    {
        name: "Visitor Name",
        cell: (row) => (
            <div className="flex items-center">
                <img
                    src={row.image || profileImage}
                    alt={row.visitorName}
                    className="w-8 h-8 rounded-full mr-2"
                />
                <span>{row.visitorName}</span>
            </div>
        ),
        sortable: true,
    },
    {
        name: "Host",
        selector: (row) => row.visitorHost || "N/A",
    },
    {
        name: "Facility",
        selector: (row) => row.facility || "N/A",
    },
    {
        name: "Start Date & Time",
        selector: (row) => row.startDate || "N/A",
    },
    {
        name: "End Date & Time",
        selector: (row) => row.endDate || "N/A",
    },
    {
        name: "Action",
        cell: (row) => (
            <div className="flex justify-center items-center space-x-2">
                <img
                    src={Cemara}
                    alt="Camera"
                    className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
                    onClick={() => openModal("Update Profile Image", row.id)}
                />
                <img
                    src={Chat}
                    alt="Chat"
                    className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
                    onClick={() => console.log("Chat clicked for:", row)}
                />
                <img
                    src={Print}
                    alt="Print"
                    className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
                    onClick={() => handlePrintClick(row)}
                />
                <img
                    src={In}
                    alt="In"
                    className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
                    onClick={() => console.log("In clicked for:", row)}
                />
                <img
                    src={Out}
                    alt="Out"
                    className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
                    onClick={() => console.log("Out clicked for:", row)}
                />
            </div>
        ),
        center: true,
    },
];


