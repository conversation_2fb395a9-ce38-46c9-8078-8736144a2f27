import React, { useState } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import CustomDropdown from "../../Global/CustomDropdown";
import Input from "../../Global/Input/Input";
import { toast } from "react-toastify";
import { updateAccessLevel } from "../../../api/facility";

// Hooks for fetching dropdown options
import { useBuildingData } from "../../../hooks/useBuildingData";
import { useFloorData } from "../../../hooks/useFloorData";
import { useRoomData } from "../../../hooks/useRoomData";

const accessAreaSchema = yup.object().shape({
  facility_id: yup.string().required("Facility is required"),
  building_id: yup.string().required("Building is required"),
  floor_id: yup.string().required("Floor is required"),
  room_id: yup.string().required("Room is required"),
});

const ViewEditAccessAreaForm = ({
  accessAreaData,
  onClose,
  refreshAccessAreas,
  defaultEditMode = false,
}) => {
  const [isEditMode, setIsEditMode] = useState(defaultEditMode);
  const [show, setShow] = useState(false);
  // For dependent dropdowns, maintain selected values in state
  const [selectedBuilding, setSelectedBuilding] = useState(
    accessAreaData.building?.building_id || ""
  );
  const [selectedFloor, setSelectedFloor] = useState(
    accessAreaData.floor?.floor_id || ""
  );

  // Use hooks to get dropdown options
  const buildingOptions = useBuildingData(accessAreaData.facility?.facility_id);
  const floorOptions = useFloorData(selectedBuilding);
  const roomOptions = useRoomData(selectedFloor);

  const {
    register,
    handleSubmit,
    control,
    reset,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(accessAreaSchema),
    defaultValues: {
      facility_id: accessAreaData.facility?.facility_id || "",
      building_id: accessAreaData.building?.building_id || "",
      floor_id: accessAreaData.floor?.floor_id || "",
      room_id: accessAreaData.room?.room_id || "",
    },
  });

  // Field configuration array
  const fields = [
    {
      label: "Facility",
      name: "facility_id",
      type: "text",
      readOnly: true,
      viewValue: accessAreaData.facility?.name || accessAreaData.facility_id,
    },
    {
      label: "Building *",
      name: "building_id",
      type: "customDropdown",
      placeholder: "Select Building",
      options: buildingOptions,
      onSelectExtra: (selectedValue, onChange) => {
        setSelectedBuilding(selectedValue);
        onChange(selectedValue);
        // Reset dependent fields.
        setValue("floor_id", "");
        setSelectedFloor("");
        setValue("room_id", "");
      },
      viewValue: accessAreaData.building?.name || accessAreaData.building_id,
    },
    {
      label: "Floor *",
      name: "floor_id",
      type: "customDropdown",
      placeholder: "Select Floor",
      options: floorOptions,
      onSelectExtra: (selectedValue, onChange) => {
        setSelectedFloor(selectedValue);
        onChange(selectedValue);
        // Reset room selection.
        setValue("room_id", "");
      },
      viewValue: accessAreaData.floor?.floor_number || accessAreaData.floor_id,
    },
    {
      label: "Room *",
      name: "room_id",
      type: "customDropdown",
      placeholder: "Select Room",
      options: roomOptions,
      viewValue: accessAreaData.room?.room_number || accessAreaData.room_id,
    },
  ];

  // Form submission handler: update access level with new room selection.
  const onSubmit = async (data) => {
    const facilityAccessLevelId = accessAreaData.facility_access_level_id;
    if (!facilityAccessLevelId) {
      toast.error("Access Level ID is missing.");
      return;
    }
    try {
      const { facility_id, ...payload } = data;
      await updateAccessLevel(accessAreaData.facility?.facility_id, facilityAccessLevelId, payload);
      toast.success("Access level updated successfully!");
      setIsEditMode(false);
      onClose();
      refreshAccessAreas();
    } catch (error) {
      console.error("Error updating access level:", error);
      toast.error(
        error.response?.data?.message || "Failed to update access level."
      );
    }
  };

  // Cancel editing: reset form to original values.
  const handleCancelEdit = () => {
    reset({
      facility_id: accessAreaData.facility?.facility_id || "",
      building_id: accessAreaData.building?.building_id || "",
      floor_id: accessAreaData.floor?.floor_id || "",
      room_id: accessAreaData.room?.room_id || "",
    });
    setSelectedBuilding(accessAreaData.building?.building_id || "");
    setSelectedFloor(accessAreaData.floor?.floor_id || "");
    setIsEditMode(false);
  };

  // Animation mount/unmount logic
  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-white w-full h-full max-w-3xl p-6 overflow-y-auto shadow-xl transform transition-transform duration-700 ease-in-out ${
          show ? "translate-x-0" : "translate-x-full"
        }`}
        style={{ willChange: "transform" }}
      >
        {/* Header */}
        <div className="flex items-center mb-4 justify-between">
          <h2 className="text-[30px] font-normal text-[#4F2683]">
            Access Level Details
          </h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
          >
            &times;
          </button>
        </div>
        <hr className="mb-6" />

        {/* Form */}
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="bg-white rounded-lg"
        >
          {fields.map((field, idx) => (
            <div key={idx} className="flex items-center mb-4">
              <label className="w-1/3 text-[16px] font-normal text-[#333333]">
                {field.label}
              </label>
              <div className="w-2/3">
                {isEditMode && !field.readOnly ? (
                  field.type === "customDropdown" ? (
                    <Controller
                      control={control}
                      name={field.name}
                      render={({ field: controllerField }) => (
                        <CustomDropdown
                          value={controllerField.value}
                          options={field.options}
                          placeholder={field.placeholder}
                          onSelect={(option) => {
                            if (field.onSelectExtra) {
                              field.onSelectExtra(option, controllerField.onChange);
                            } else {
                              controllerField.onChange(option);
                            }
                          }}
                          bgColor="bg-white text-black"
                          textColor="text-black"
                          hoverBgColor="hover:bg-[#4F2683]"
                          borderColor="border-gray-300"
                          className="p-2 border h-11 rounded focus:outline-none focus:ring-1"
                        />
                      )}
                    />
                  ) : (
                    <Input
                      type={field.type}
                      {...register(field.name)}
                      disabled={!isEditMode}
                      className="w-full border rounded p-2"
                    />
                  )
                ) : (
                  <Input
                    type={field.type === "customDropdown" ? "text" : field.type}
                    value={field.viewValue || ""}
                    disabled
                    className="w-full border-none text-[#8F8F8F]"
                  />
                )}
                {errors[field.name] && (
                  <p className="text-red-500 text-sm mt-1">
                    {errors[field.name].message}
                  </p>
                )}
              </div>
            </div>
          ))}

          {/* Action Buttons */}
          <div className="flex gap-4 justify-end mt-6">
            {!isEditMode ? (
              <button
                type="button"
                onClick={() => setIsEditMode(true)}
                className="px-4 py-2 bg-[#4F2683] text-white rounded"
              >
                Edit
              </button>
            ) : (
              <>
                <button
                  type="button"
                  onClick={handleCancelEdit}
                  className="px-4 py-2 bg-[#979797] text-white rounded"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-[#4F2683] text-white rounded"
                >
                  Save
                </button>
              </>
            )}
          </div>
        </form>
      </div>
    </div>
  );
};

export default ViewEditAccessAreaForm;
