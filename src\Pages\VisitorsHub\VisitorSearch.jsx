import React from "react";
import SearchBar from "../../Components/Global/SearchBar";

const VisitorSearch = ({
  placeholder,
  searchTerm,
  onInputChange,
  results,
  onResultClick,
  isDropdownVisible,
  containerRef,
  onCreateClick, // Add the create visitor handler
}) => {
  return (
    <div className="w-full sm:w-auto relative" ref={containerRef}>
      <SearchBar
        placeholder={placeholder}
        value={searchTerm}
        onInputChange={onInputChange}
        borderColor="#4F2683"
      />
      {isDropdownVisible && (
        <div
          className="w-full mt-2 border absolute p-2 bg-white z-10 rounded-md shadow-lg overflow-y-auto"
          style={{ maxHeight: "200px" }}
        >
          {results.length > 0 ? (
            results.map((visitor) => (
              <div
                key={visitor.id}
                className="flex items-center gap-2 p-2 border-b cursor-pointer hover:bg-gray-100"
                onClick={() => onResultClick(visitor)}
              >
                <img
                  src={visitor.image}
                  alt={visitor.visitorName}
                  className="w-10 h-10 rounded"
                />
                <div>
                  <h2 className="font-semibold">{visitor.visitorName}</h2>
                  <div className="flex gap-2">
                    <p className="text-sm text-gray-600">{visitor.hostName}</p>
                    <p className="text-sm text-gray-600">,EID-{visitor.eid}</p>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <button
              className="text-[#4F2683] w-full text-left"
              onClick={onCreateClick} // Trigger the create visitor handler
            >
              Create New Visitor
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default VisitorSearch;
