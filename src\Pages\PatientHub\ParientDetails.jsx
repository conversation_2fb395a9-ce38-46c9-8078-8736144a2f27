import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import DataTable from "react-data-table-component";
import Demographic from '../../Components/PatientHub/Demographic';
import GuestList from '../../Components/PatientHub/GuestList';
import FriendsFamily from '../../Components/PatientHub/FriendsFamily';
import DeniedGuests from '../../Components/PatientHub/DeniedGuests';
import HL7Messages from '../../Components/PatientHub/HL7Messages';
import Ptienthubdemo from '../../Images/Ptienthubdemo.svg';
import DetailsCard from '../../Components/Global/DetailsCard';
import EditPhotoModal from '../../Components/Global/ImageAndCamera/EditPhotoModal';
import { IoIosArrowBack } from "react-icons/io";
import { getPatientDetails, getAllPatientHistory } from '../../api/PatientHub';
import userImg from "../../Images/fromimg.svg";
import { useTranslation } from 'react-i18next';
import { updatePatientImage } from '../../api/PatientHub';
import { getMediaByModel } from '../../api/global';
import imageCompression from 'browser-image-compression';
import Device from '../../Components/PatientHub/Device';
import DeviceGroup from '../../Components/PatientHub/DeviceGroup';

const ParientDatailes = () => {
  const { t } = useTranslation();
  const [selectedTab, setSelectedTab] = useState(t('patient_hub.tabs.demographic'));
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [profileImage, setProfileImage] = useState(null);
  const [patientDetails, setPatientDetails] = useState(null);
  const [isHistoryPanelOpen, setIsHistoryPanelOpen] = useState(false);
  const [historyData, setHistoryData] = useState([]);
  const [isLoadingDetails, setIsLoadingDetails] = useState(false);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [error, setError] = useState(null);
  const { patientId } = useParams();
  const navigate = useNavigate();

  const customStyles = {
    headCells: {
      style: {
        fontWeight: 500,
        fontSize: "14px",
        color: "#9971CB",
      },
    },
    rows: {
      style: {
        borderBottom: "1px solid #e0e0e0",
      },
    },
  };

  const columns = [
    { name: t('patient_hub.effective_date'), selector: row => row.effectiveDate, sortable: true, width: "18%" },
    {
      name: t('patient_hub.changed_by'),
      selector: row => row.changedBy,
      cell: row => (
        <div className="flex items-center justify-center">
          <img src={row.avatar} alt="avatar" style={{ width: 30, height: 30, borderRadius: "50%", marginRight: 10 }} />
          <span
            className="truncate"
            style={{ maxWidth: '100px' }}
          >
            {row.changedBy}
          </span>
        </div>
      ),
      width: "25%"
    },
    {
      name: t('patient_hub.event_type'),
      selector: row => row.eventType,
      cell: row => <span className="bg-[#f4f2f7] rounded-xl px-5 py-1 text-[#4F2683]">{row.eventType}</span>,
      width: "15%",
      center: true,
    },
    { name: t('patient_hub.field_changed'), selector: row => row.fieldChanged, width: "14%" },
    { name: t('patient_hub.from'), selector: row => row.from, center: true, width: "14%" },
    { name: t('patient_hub.to'), selector: row => row.to, center: true, width: "14%" },
  ];
  
  useEffect(() => {
    const fetchPatientDetails = async () => {
      setIsLoadingDetails(true);
      setError(null);

      try {
        const response = await getPatientDetails({ patient_id: patientId });
        const data = response.data;

        setPatientDetails({
          name: `${data.first_name} ${data.last_name}`,
          mrn: data.mrn,
          type: data.appointment_type_name,
          status: data.appointment_status_name,
          confidential: data.confidentiality_code === 1 ? "Yes" : "No",
          facility: data.facility_name || "Not Specified",
          appointmentDate: new Date(data.appointment_date).toLocaleString(),
        });

        const imageId = data.image_id || data.image;
        if (imageId) {
          const media = await getMediaByModel("Patient", {
            key: "image",
            value: imageId,
          });
          setProfileImage(media.value);
        }
      } catch (err) {
        console.error("Error fetching patient details:", err);
        setError("Failed to load patient details. Please try again.");
      } finally {
        setIsLoadingDetails(false);
      }
    };

    if (patientId) {
      fetchPatientDetails();
    }
  }, [patientId]);

  useEffect(() => {
    const fetchHistory = async () => {
      setIsLoadingHistory(true);
      setError(null);
      try {
        const response = await getAllPatientHistory({});
        if (response.data.length === 0) {
          setError("No history found for this patient.");
          setHistoryData([]);
          return;
        }
        const formattedData = response.data.map(item => ({
          effectiveDate: new Date(item.effective_date).toLocaleString(),
          changedBy: item.modified_by,
          avatar: userImg,
          eventType: item.event_type,
          fieldChanged: item.field_changes,
          from: item.old_value,
          to: item.new_value,
        }));
        setHistoryData(formattedData);
      } catch (error) {
        console.error("Error fetching patient history:", error);
        setError("Failed to load patient history. Please try again.");
      } finally {
        setIsLoadingHistory(false);
      }
    };

    if (isHistoryPanelOpen && patientId) fetchHistory();
  }, [isHistoryPanelOpen, patientId]);

  const handleHistoryOpen = () => setIsHistoryPanelOpen(true);

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      setIsHistoryPanelOpen(false);
    }
  };

  // Helper function to convert base64 to File
  const dataURLtoFile = (dataurl, filename) => {
    const arr = dataurl.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename, { type: mime });
  };

  // Updated image handler with compression
  const handleImageCaptured = async (imageSrc) => {
    try {
      let file;
      let objectUrl = null;
      
      // Convert base64 to File
      if (typeof imageSrc === 'string' && imageSrc.startsWith('data:image')) {
        file = dataURLtoFile(imageSrc, 'profile.jpg');
      } 
      // Handle direct file uploads
      else if (imageSrc instanceof File) {
        file = imageSrc;
      } 
      // Handle Blob objects
      else if (imageSrc instanceof Blob) {
        file = new File([imageSrc], 'profile.jpg', { type: imageSrc.type });
      }
      // Unsupported type
      else {
        throw new Error('Unsupported image type');
      }

      // Compress image
      const options = {
        maxSizeMB: 0.5, // Compress to max 500KB
        maxWidthOrHeight: 500, // Maintain aspect ratio
        useWebWorker: true // For better performance
      };
      const compressedFile = await imageCompression(file, options);

      // Create temporary URL for immediate UI update
      objectUrl = URL.createObjectURL(compressedFile);
      setProfileImage(objectUrl);

      // Prepare FormData for upload
      const formData = new FormData();
      formData.append('image', compressedFile);

      // Update patient image
      await updatePatientImage(patientId, formData);

      // Refetch updated patient details
      const response = await getPatientDetails({ patient_id: patientId });
      const imageId = response.data.image_id || response.data.image;
      
      if (imageId) {
        const media = await getMediaByModel("Patient", {
          key: "image",
          value: imageId,
        });
        // Update with permanent URL
        setProfileImage(media.value);
      }
      
      // Clean up temporary URL
      if (objectUrl) {
        URL.revokeObjectURL(objectUrl);
      }
    } catch (err) {
      console.error("Failed to save image:", err);
      setError("Failed to update profile image. Please try again.");
    } finally {
      setIsModalOpen(false);
    }
  };

  const tabKeys = [
    'patient_hub.tabs.demographic',
    'patient_hub.tabs.guest_list',
    'patient_hub.tabs.friends_family',
    'patient_hub.tabs.denied_guests',
    'patient_hub.tabs.hl7_messages',
    'Devices',
    'Device Group'
  ];

  return (
    <div className="bg-gray-100 min-h-screen p-8 pl-20 pt-20">
      {/* Profile Section */}
      <div className="flex items-center text-[#4F2683]">
        <div
          className="flex items-center gap-1 cursor-pointer"
          onClick={() => navigate("/patient-hub")}
        >
          <IoIosArrowBack className="text-[#4F2683] font-normal text-[24px]" />
          <h2 className="font-normal text-[24px]">{t('patient_hub.title')}</h2>
        </div>
      </div>
      {error && <div className="text-red-500 mb-4">{error}</div>}
      {isLoadingDetails ? (
        <div>{t('patient_hub.loading_patient_details')}</div>
      ) : (
        <DetailsCard
          OpenPhotoModal={() => setIsModalOpen(true)}
          handleHistoryOpen={handleHistoryOpen}
          profileImage={profileImage}
          defaultImage={Ptienthubdemo}
          showHistoryButton={true}
          name={patientDetails?.name}
          additionalFields={[
            { label: t('patient_hub.mrn'), value: patientDetails?.mrn },
            { label: t('patient_hub.type'), value: patientDetails?.type },
            { label: t('patient_hub.status'), value: patientDetails?.status },
            { label: t('patient_hub.confidential'), value: patientDetails?.confidential },
            { label: t('patient_hub.facility'), value: patientDetails?.facility },
          ]}
        />
      )}

      {/* Sidebar and Main Content */}
      <div className="flex">
        <div className="w-[12%] mt-8">
          {tabKeys.map((tabKey) => (
            <button
              key={tabKey}
              className={`block w-full text-left p-2 mb-2 ${selectedTab === t(tabKey) || (tabKey === 'Devices' && selectedTab === 'Devices') || (tabKey === 'Device Group' && selectedTab === 'Device Group') ? 'text-[#4F2683] border-l-2 border-[#4F2683]' : 'text-gray-700'}`}
              onClick={() => setSelectedTab(tabKey === 'Devices' || tabKey === 'Device Group' ? tabKey : t(tabKey))}
            >
              {tabKey === 'Devices' || tabKey === 'Device Group' ? tabKey : t(tabKey)}
            </button>
          ))}
        </div>

        {/* Main Content */}
        <div className="w-[88%] pt-4">
          {selectedTab === t('patient_hub.tabs.demographic') && <Demographic patientId={patientId} />}
          {selectedTab === t('patient_hub.tabs.guest_list') && <GuestList patientId={patientId} />}
          {selectedTab === t('patient_hub.tabs.friends_family') && <FriendsFamily patientId={patientId} />}
          {selectedTab === t('patient_hub.tabs.denied_guests') && <DeniedGuests patientId={patientId} />}
          {selectedTab === t('patient_hub.tabs.hl7_messages') && <HL7Messages patientId={patientId} mrn={patientDetails?.mrn} />}
          {selectedTab === 'Devices' && <Device />}
          {selectedTab === 'Device Group' && <DeviceGroup />}
        </div>
      </div>

      {/* History Panel */}
      {isHistoryPanelOpen && (
        <div
          className={`fixed top-0 left-0 h-full w-full bg-gray-900 bg-opacity-50 z-40`}
          onClick={handleOverlayClick}>
          <div
            className={`fixed top-0 right-0 h-full w-[80%] p-2 bg-white shadow-lg rounded-l-md transform transition-transform duration-300 z-50`}
          >
            <div className="flex justify-between border-b-2 pb-4 items-center pt-2 ">
              <h1 className=" pl-2 font-normal text-[23px] text-[#4F2683]">{t('patient_hub.view_history')}</h1>
              <button
                className=" rounded-full text-2xl text-white w-8 h-8 flex items-center justify-center bg-[#4F2683]"
                onClick={() => setIsHistoryPanelOpen(false)}
              >&times;</button>
            </div>
            {isLoadingHistory ? (
              <div>{t('patient_hub.loading_history')}</div>
            ) : historyData.length === 0 ? (
              <div className="text-gray-500 text-center mt-4">{t('patient_hub.no_history_found')}</div>
            ) : (
              <DataTable
                columns={columns}
                data={historyData}
                customStyles={customStyles}
                fixedHeader
                fixedHeaderScrollHeight="700px"
              />
            )}
          </div>
        </div>
      )}

      {/* Edit Photo Modal */}
      {isModalOpen && (
        <EditPhotoModal
          onClose={() => setIsModalOpen(false)}
          onSave={handleImageCaptured}
        />
      )}
    </div>
  );
};

export default ParientDatailes;