import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { updateDocument } from "../../../api/identity";
import { toast } from "react-toastify";
import Input from "../../Global/Input/Input";
import CustomDropdown from "../../Global/CustomDropdown";
import DateInput from "../../Global/Input/DateInput";
import { useCountryMasterData } from "../../../hooks/useCountryMasterData";
import { useStateMasterData } from "../../../hooks/useStateMasterData";

// Validation schema
const documentSchema = yup.object().shape({
  document_name: yup.string().required("Document name is required"),
  document_type: yup.string().required("Document type is required"),
  document_number: yup.string().required("Document number is required"),
  issue_date: yup.date()
    .typeError('Invalid date')
    .required("Issue date is required"),
  expiry_date: yup.date()
    .typeError('Invalid date')
    .required("Expiry date is required"),
  status: yup.string().required("Status is required"),
  country_id: yup.string().required("Country is required"),
  state_id: yup.string().required("State is required"),
  other_issuer: yup.string(),
  note: yup.string(),
});

const ViewEditDocumentForm = ({ documentData, onUpdate, onClose }) => {
  const [isEditMode, setIsEditMode] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState(documentData?.country_id || null);
  const [show, setShow] = useState(false);

  // Master data hooks
  const { countries } = useCountryMasterData();
  const states = useStateMasterData(selectedCountry);

  const {
    register,
    handleSubmit,
    control,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(documentSchema),
  });

  // Reset form when documentData changes
  useEffect(() => {
    if (documentData) {
      setSelectedCountry(documentData.country_id || null);
      reset({
        document_name: documentData.document_name || "",
        document_type: documentData.document_type || "",
        document_number: documentData.document_number || "",
        issue_date: documentData.issue_date ? new Date(documentData.issue_date) : null,
        expiry_date: documentData.expiry_date ? new Date(documentData.expiry_date) : null,
        status: documentData.status || "",
        country_id: documentData.country_id?.toString() || "",
        state_id: documentData.state_id?.toString() || "",
        other_issuer: documentData.other_issuer || "",
        note: documentData.note || "",
      });
    }
  }, [documentData, reset]);

  useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const documentTypeOptions = [
    { value: "passport", label: "Passport" },
    { value: "driver_license", label: "Driver's License" },
    { value: "national_id", label: "National ID" },
    { value: "birth_certificate", label: "Birth Certificate" },
    { value: "social_security", label: "Social Security Card" },
    { value: "visa", label: "Visa" },
    { value: "other", label: "Other" },
  ];

  const statusOptions = [
    { value: "Active", label: "Active" },
    { value: "Expired", label: "Expired" },
    { value: "Pending", label: "Pending" },
    { value: "Cancelled", label: "Cancelled" },
  ];

  const handleCancelEdit = () => {
    setIsEditMode(false);
    reset();
  };

  const onSubmitForm = async (data) => {
    setLoading(true);
    try {
      if (!documentData.id) {
        throw new Error("Invalid document ID");
      }

      console.log("Update form data before formatting:", data);

      // Format dates properly
      const formattedData = {
        ...data,
        issue_date: data.issue_date instanceof Date ? data.issue_date.toISOString().split('T')[0] : data.issue_date,
        expiry_date: data.expiry_date instanceof Date ? data.expiry_date.toISOString().split('T')[0] : data.expiry_date,
        country_id: parseInt(data.country_id),
        state_id: parseInt(data.state_id),
      };

      // Remove empty optional fields
      if (!formattedData.other_issuer) delete formattedData.other_issuer;
      if (!formattedData.note) delete formattedData.note;

      console.log("Formatted update data being sent:", formattedData);
      console.log("Document ID:", documentData.id);

      const response = await updateDocument(documentData.id, formattedData);
      console.log("Update document response:", response);

      toast.success("Document updated successfully!");
      onUpdate();
    } catch (error) {
      console.error("Error updating document:", error);
      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.error ||
                          error.message ||
                          "Failed to update document. Please try again.";
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const formFields = [
    {
      name: "document_name",
      label: "Document Name",
      type: "text",
      viewValue: documentData?.document_name || "",
    },
    {
      name: "document_type",
      label: "Document Type",
      type: "customDropdown",
      options: documentTypeOptions,
      viewValue: documentTypeOptions.find(
        (opt) => opt.value === (documentData?.document_type)
      )?.label || documentData?.document_type || "",
    },
    {
      name: "document_number",
      label: "Document Number",
      type: "text",
      viewValue: documentData?.document_number || "",
    },
    {
      name: "status",
      label: "Status",
      type: "customDropdown",
      options: statusOptions,
      viewValue: statusOptions.find(
        (opt) => opt.value === documentData?.status
      )?.label || documentData?.status || "",
    },
    {
      name: "issue_date",
      label: "Issue Date",
      type: "date",
      viewValue: documentData?.issue_date
        ? new Date(documentData.issue_date).toLocaleDateString()
        : "",
    },
    {
      name: "expiry_date",
      label: "Expiry Date",
      type: "date",
      viewValue: documentData?.expiry_date
        ? new Date(documentData.expiry_date).toLocaleDateString()
        : "",
    },
    {
      name: "country_id",
      label: "Country",
      type: "customDropdown",
      options: countries.map(country => ({
        value: country.id.toString(),
        label: country.name
      })),
      viewValue: countries.find(country => country.id.toString() === documentData?.country_id?.toString())?.name || "",
    },
    {
      name: "state_id",
      label: "State",
      type: "customDropdown",
      options: states.map(state => ({
        value: state.id.toString(),
        label: state.name
      })),
      viewValue: states.find(state => state.id.toString() === documentData?.state_id?.toString())?.name || "",
    },
    {
      name: "other_issuer",
      label: "Other Issuer",
      type: "text",
      viewValue: documentData?.other_issuer || "",
    },
    {
      name: "note",
      label: "Note",
      type: "textarea",
      viewValue: documentData?.note || "",
    },
  ];

  return (
     <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
            <div
                className={`bg-white w-full h-full max-w-5xl p-2 rounded-lg shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"
                    }`}
                style={{ willChange: "transform" }}
            > 
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-semibold text-[#4F2683]">
            {isEditMode ? "Edit Document" : "View Document"}
          </h2>
          <button
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
            className="text-gray-500 hover:text-gray-700 text-2xl"
          >
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit(onSubmitForm)} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {formFields.map((field) => (
              <div key={field.name}>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {field.label}
                </label>
                {isEditMode ? (
                  field.type === "customDropdown" ? (
                    <Controller
                      name={field.name}
                      control={control}
                      render={({ field: controllerField }) => (
                        <CustomDropdown
                          value={controllerField.value}
                          options={field.options}
                          placeholder={`Select ${field.label.toLowerCase()}`}
                          onSelect={(value) => {
                            controllerField.onChange(value);
                            if (field.name === "country_id") {
                              setSelectedCountry(value);
                            }
                          }}
                          className="w-full"
                          disabled={field.name === "state_id" && !selectedCountry}
                        />
                      )}
                    />
                  ) : field.type === "date" ? (
                    <Controller
                      name={field.name}
                      control={control}
                      render={({ field: controllerField }) => (
                        <DateInput
                          value={controllerField.value}
                          onChange={controllerField.onChange}
                          placeholder={`Select ${field.label.toLowerCase()}`}
                        />
                      )}
                    />
                  ) : field.type === "textarea" ? (
                    <textarea
                      {...register(field.name)}
                      placeholder={`Enter ${field.label.toLowerCase()}`}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#4F2683] focus:border-transparent"
                      rows={3}
                    />
                  ) : (
                    <Input
                      {...register(field.name)}
                      placeholder={`Enter ${field.label.toLowerCase()}`}
                      className="w-full"
                    />
                  )
                ) : field.type === "textarea" ? (
                  <textarea
                    value={field.viewValue || ""}
                    disabled
                    className="w-full px-3 py-2 border-none text-[#8F8F8F] bg-transparent resize-none"
                    rows={3}
                  />
                ) : (
                  <Input
                    type="text"
                    value={field.viewValue || ""}
                    disabled
                    className="w-full border-none text-[#8F8F8F]"
                  />
                )}
                {errors[field.name] && (
                  <p className="text-red-500 text-sm mt-1">
                    {errors[field.name].message}
                  </p>
                )}
              </div>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-4 mt-6">
            {!isEditMode ? (
              <button
                type="button"
                onClick={() => setIsEditMode(true)}
                className="px-6 py-2 bg-[#4F2683] text-white rounded-lg hover:bg-[#3d1f66]"
              >
                Edit
              </button>
            ) : (
              <>
                <button
                  type="button"
                  onClick={handleCancelEdit}
                  className="px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600"
                  disabled={loading}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-6 py-2 bg-[#4F2683] text-white rounded-lg hover:bg-[#3d1f66] disabled:opacity-50"
                  disabled={loading}
                >
                  {loading ? "Updating..." : "Update Document"}
                </button>
              </>
            )}
          </div>
        </form>
      </div>
    </div>
  );
};

export default ViewEditDocumentForm;